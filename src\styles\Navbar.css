.logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.logout-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.logout-btn svg {
  width: 20px;
  height: 20px;
}

.logout-btn .title {
  font-size: 14px;
  font-weight: 500;
}

/* Add responsive styles */
@media (max-width: 768px) {
  .logout-btn .title {
    display: none;
  }

  .logout-btn {
    padding: 8px;
  }
}

.user-info {
  color: #fff;
  display: flex;
  align-items: center;
  gap: 7px;
  cursor: pointer;
}

.main-header.scrolled .user-info {
  color: #333;
}

.header-actions img,
.header-actions svg {
  width: 15px;
  height: 15px;
}

.main-header.scrolled .header-actions img {
  filter: invert(1);
}

.header-actions img:hover {
  filter: brightness(0) saturate(100%) invert(34%) sepia(100%) saturate(379%)
    hue-rotate(329deg) brightness(95%) contrast(95%) !important;
}

.user-info:hover span,
.user-info:hover img {
  filter: brightness(0) saturate(100%) invert(34%) sepia(100%) saturate(379%)
    hue-rotate(329deg) brightness(95%) contrast(95%) !important;
  color: #b45434;
}
