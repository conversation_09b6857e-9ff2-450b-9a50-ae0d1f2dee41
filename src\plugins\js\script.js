const html = document.querySelector("html");
const pageDirection = document.querySelector("html").getAttribute("dir");
const body = document.querySelector("body");
const cursor = document.querySelector(".cursor");
const header = document.querySelector(".main-header");
const flotingMenu = document.querySelector(".floting-menu");
const flotingMenuToggle = document.querySelector(".floting-menu-toggle");
const fontResizer = document.querySelectorAll(".font-control__item");
const themeToggler = document.querySelectorAll(".theme-control__item");
const swiperPagination = document.querySelectorAll(".swiper-pagination");
const swiperControl = document.querySelectorAll(".swiper-control");
// const fontDropdownLink = document.querySelector('.font-dropdown .dropdown-menu').querySelectorAll('.dropdown-item');
// scroll top
function scrollFunction() {
  backTopBtn = document.querySelector(".back-top-btn");
  if (
    document.body.scrollTop > 300 ||
    document.documentElement.scrollTop > 300
  ) {
    (backTopBtn.style.opacity = "1"), (backTopBtn.style.visibility = "visible");
  } else {
    (backTopBtn.style.opacity = "0"), (backTopBtn.style.visibility = "none");
  }
}

function backTotop() {
  // e.preventDefault();
  document.body.scrollTo({
    top: 0,
    behavior: "smooth",
  });
  document.documentElement.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}
// Add an event listener for the scroll event
window.addEventListener("scroll", function () {
  // Check if the page has been scrolled more than 0 pixels
  if (window.scrollY > 0) {
    // Add the class to the header
    header.classList.add("scrolled");
  } else {
    // Remove the class if not scrolled
    header.classList.remove("scrolled");
  }
  scrollFunction();
});

// Document load
document.addEventListener("DOMContentLoaded", () => {
  //Preloader
  const preloader = document.getElementById('preloader');
  const content = document.querySelector('.preloader-content');
  const slices = document.querySelectorAll('.slice');
  const mainContent = document.getElementById('hero');
  function animatePreloaderOut() {
    content.classList.add('fade-out');

    setTimeout(() => {
      slices[0].classList.add('slide1');
      slices[1].classList.add('slide2');
    }, 600);

    setTimeout(() => {
      preloader.style.display = 'none';
      mainContent.style.display = 'block';
      document.body.style.overflow = 'auto';
    }, 1400);
  }
  window.addEventListener('load', animatePreloaderOut);

  // Number animation
  // Function to animate numbers with improved accuracy
  function animateNumbers() {
    const countToElements = document.querySelectorAll('.count-to');
    countToElements.forEach(element => {
      const mainEndValue = element.getAttribute('data-main-end-value');
      let endValue = parseFloat(mainEndValue.replace(/[^\d.-]/g, '')); // Extract the numeric part
      if (mainEndValue.includes('M')) {
        endValue *= 1000000; // Convert millions to the numeric value
      }
      const startValue = 0;
      const duration = 2500; // Increase animation duration to 3 seconds for smoother animation
      let animationComplete = false; // Flag to track animation completion
      const startTime = performance.now();

      function updateNumber(currentTime) {
        if (!animationComplete) {
          const progress = currentTime - startTime;
          const currentValue = Math.min(Math.floor(progress / duration * endValue), endValue); // Ensure current value doesn't exceed end value
          element.textContent = formatNumber(currentValue); // Format number with commas and suffix if applicable

          if (progress < duration && currentValue < endValue) {
            requestAnimationFrame(updateNumber);
          } else {
            element.textContent = formatNumber(endValue); // Ensure final value is displayed accurately
            animationComplete = true; // Set flag to indicate animation completion
          }
        }
      }

      requestAnimationFrame(updateNumber);
    });
  }

  // Format number with commas and suffix if applicable
  function formatNumber(value) {
    if (value >= 1000000) {
      return (value / 1000000) + 'M'; // Format as millions with one decimal place
    }
    else if (value >= 1000) {
      return (value / 1000) + 'K'; // Format as millions with one decimal place
    }
    else {
      return value.toLocaleString(); // Format with commas
    }
  }

  // Intersection Observer to run animation when .about-tabs appears on screen
  const statisticsCountSection = document.querySelector('.portal-statistics');
  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        animateNumbers();
        observer.unobserve(entry.target); // Stop observing once animation runs
      }
    });
  }, { threshold: 0.1 });

  observer.observe(statisticsCountSection);
});

// Initialize Swiper
// Hero caption swiper (text)
const homeSwiper = new Swiper(".hero-slider", {
  slidesPerView: 1,
  effect: "fade",
  speed: 1200,
  loop: false,
  spaceBetween: 0,
  watchSlidesVisibility: true,
  watchSlidesProgress: true,
});

// Background swiper
const homeBgSwiper = new Swiper(".hero-bg-slider", {
  slidesPerView: 1,
  effect: "fade",
  speed: 1200,
  loop: false,
  spaceBetween: 0,
  watchSlidesProgress: true,
  navigation: {
    nextEl: '.hero-slider-next',
    prevEl: '.hero-slider-prev',
  },
});

// Hero image swiper (main one)
const heroImgSwiper = new Swiper(".hero-img-slider", {
  effect: "coverflow",
  speed: 1200,
  grabCursor: true,
  centeredSlides: true,
  loop: false,
  coverflowEffect: {
    rotate: 0,
    stretch: 0,
    depth: 100,
    modifier: 3,
    slideShadows: true,
  },
  autoplay: {
    delay: 3000,
    disableOnInteraction: false,
  },
  breakpoints: {
    640: { slidesPerView: 3 },
    768: { slidesPerView: 3 },
    1024: { slidesPerView: 3 },
    1560: { slidesPerView: 3 },
  },
});

// Main swiper controls the others directly (safe)
heroImgSwiper.controller.control = [homeSwiper, homeBgSwiper];

// Manual sync from homeSwiper (text) to heroImgSwiper
homeSwiper.on('slideChangeTransitionStart', () => {
  const index = homeSwiper.activeIndex;
  if (heroImgSwiper.activeIndex !== index) {
    heroImgSwiper.slideToLoop(index, 1200);
  }
});

// Manual sync from homeBgSwiper (background) to heroImgSwiper
homeBgSwiper.on('slideChangeTransitionStart', () => {
  const index = homeBgSwiper.activeIndex;
  if (heroImgSwiper.activeIndex !== index) {
    heroImgSwiper.slideToLoop(index, 1200);
  }
});

const eservicesSwiper = new Swiper(".e-services-slider", {
  // effect: "coverflow",
  speed: 1200,
  slidesPerView: 1,
  grabCursor: true,
  spaceBetween: 0,
  centeredSlides: true,
  loop: true,
  pagination: {
    el: '.swiper-pagination',
    clickable: true,
  },
  autoplay: {
    delay: 3000,
    disableOnInteraction: false,
  },
  breakpoints: {
    640: { slidesPerView: 1 },
    768: { slidesPerView: 2 },
    768: { slidesPerView: 3 },
    1024: { slidesPerView: 3 },
    1560: { slidesPerView: 3 },
  },
});
// Init tooltip
const tooltipTriggerList = document.querySelectorAll(
  '[data-bs-toggle="tooltip"]'
);
const tooltipList = [...tooltipTriggerList].map(
  (tooltipTriggerEl) => new bootstrap.Tooltip(tooltipTriggerEl)
);
//Font control

if (document.querySelector("[data-fancybox]")) {
  Fancybox.bind("[data-fancybox]", {
    // Your options go here
  });
}
if (document.querySelector(".copyrights .copyrights-year")) {
  document.querySelector(".copyrights .copyrights-year").innerHTML =
    new Date().getFullYear();
}

AOS.init();

// You can also pass an optional settings object
// below listed default settings
AOS.init({
  // Global settings:
  disable: false, // accepts following values: 'phone', 'tablet', 'mobile', boolean, expression or function
  startEvent: "DOMContentLoaded", // name of the event dispatched on the document, that AOS should initialize on
  initClassName: "aos-init", // class applied after initialization
  animatedClassName: "aos-animate", // class applied on animation
  useClassNames: false, // if true, will add content of `data-aos` as classes on scroll
  disableMutationObserver: false, // disables automatic mutations' detections (advanced)
  debounceDelay: 50, // the delay on debounce used while resizing window (advanced)
  throttleDelay: 99, // the delay on throttle used while scrolling the page (advanced)

  // Settings that can be overridden on per-element basis, by `data-aos-*` attributes:
  offset: 0, // offset (in px) from the original trigger point
  delay: 0, // values from 0 to 3000, with step 50ms
  duration: 700, // values from 0 to 3000, with step 50ms
  easing: "ease", // default easing for AOS animations
  once: true, // whether animation should happen only once - while scrolling down
  mirror: false, // whether elements should animate out while scrolling past them
  anchorPlacement: "top-bottom", // defines which position of the element regarding to window should trigger the animation
});


// Change map section background
const tabToImageId = {
  "ncw-tabs-1-tab": "bg-img-1",
  "ncw-tabs-2-tab": "bg-img-2",
  "ncw-tabs-3-tab": "bg-img-3"
};

const tabs = document.querySelectorAll('.ncwMap-tabs .nav-link');
const bgImages = document.querySelectorAll('.map-bg');

tabs.forEach(tab => {
  tab.addEventListener('click', function () {
    const selectedId = tabToImageId[this.id];

    bgImages.forEach(img => {
      img.classList.remove('active');
    });

    const selectedImg = document.getElementById(selectedId);
    if (selectedImg) {
      selectedImg.classList.add('active');
    }
  });
});
