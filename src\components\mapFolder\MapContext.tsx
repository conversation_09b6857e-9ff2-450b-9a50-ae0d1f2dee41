import React, { createContext, PropsWithChildren, useState } from "react";

export interface MapContextType {
  map: any;
  setMap: React.Dispatch<React.SetStateAction<any>>;
  mapView: any;
  setMapView: React.Dispatch<React.SetStateAction<any>>;
  mapInfo: any;
  setMapInfo: React.Dispatch<React.SetStateAction<any>>;
}

export const MapContext = createContext<MapContextType | null>(null);

export const MapProvider = ({ children }: PropsWithChildren) => {
  const [map, setMap] = useState(null);
  const [mapView, setMapView] = useState(null);
  const [mapInfo, setMapInfo] = useState(null);

  return (
    <MapContext.Provider
      value={{ map, setMap, mapView, setMapView, mapInfo, setMapInfo }}
    >
      {children}
    </MapContext.Provider>
  );
};
