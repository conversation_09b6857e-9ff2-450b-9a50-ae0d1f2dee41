import { atom } from "@mongez/react-atom";
import { IOneGuideListItem } from "../../types";

type OneGuideListAtom = {
  data: IOneGuideListItem[];
  isLoading: boolean;
  error: string;
  pagination: number;
};

type OneGuideListAtomActions = {
  startLoading: () => void;
  endLoading: () => void;
  failed: (error: string) => void;
  success: (data: IOneGuideListItem[], pagination?: number) => void;
};

export const oneGuideListAtom = atom<OneGuideListAtom, OneGuideListAtomActions>(
  {
    key: "one-guide-list",
    default: {
      data: [],
      isLoading: false,
      error: "",
      pagination: 0,
    },

    actions: {
      startLoading: () => {
        oneGuideListAtom.change("isLoading", true);
      },

      endLoading() {
        oneGuideListAtom.change("isLoading", false);
      },

      failed: (error: string) => {
        oneGuideListAtom.change("error", error);
      },

      success: (data: IOneGuideListItem[], pagination?: number) => {
        oneGuideListAtom.merge({
          data,
          pagination,
        });
      },
    },
  }
);
