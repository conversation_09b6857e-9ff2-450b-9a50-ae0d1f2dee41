import tasks_image from "../assets/images/tasks.png";
import { useTranslation } from "react-i18next";
import PageLayout from "../components/layouts/PageLayout";
import SectionTitle from "../components/common/SectionTitle";
import { motion } from "framer-motion";
import { staggerContainer, cardVariant } from "../utils/animations";

export default function AboutPage() {
  const { t } = useTranslation();

  return (
    <PageLayout breadcrumbTitle={t("navbar.about")} backgroundAlt="Map Vector">
      <div className="container-fluid">
        <SectionTitle
          title={t("aboutManagement.managementTitle.data")}
          coloredPart={t("aboutManagement.managementTitle.geo")}
          description={t("aboutManagement.managementDescription")}
        />
      </div>

      <div className="container-fluid">
        <SectionTitle
          title={t("aboutManagement.tasks.title.tasks")}
          coloredPart={t("aboutManagement.tasks.title.management")}
        />

        <div style={{ position: "relative" }}>
          <div className="task-image">
            <img src={tasks_image} className="tasks-image" alt="" />
          </div>
          <motion.div
            className="management_tasks"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.1 }}
            variants={staggerContainer}
          >
            {[...new Array(11)].map((_, index) => {
              return (
                <motion.div
                  className="task"
                  style={
                    index === 10
                      ? {
                          gridColumn: "span 2",
                          margin: "0 auto",
                          maxWidth: "500px",
                          paddingInlineEnd: "0",
                        }
                      : {}
                  }
                  key={index}
                  variants={cardVariant}
                >
                  <h5>{index + 1}</h5>
                  <p>{t(`aboutManagement.tasks.items.${index}`)}</p>
                </motion.div>
              );
            })}
          </motion.div>
        </div>
      </div>
    </PageLayout>
  );
}
