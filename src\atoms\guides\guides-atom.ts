import { atom } from "@mongez/react-atom";
import { IGuideItem } from "../../types";

type GuidesAtom = {
  data: IGuideItem[];
  isLoading: boolean;
  error: string;
  pagination: number;
};

type GuidesAtomActions = {
  startLoading: () => void;
  endLoading: () => void;
  failed: (error: string) => void;
  success: (data: IGuideItem[], pagination?: number) => void;
};

export const guidesAtom = atom<GuidesAtom, GuidesAtomActions>({
  key: "guides",
  default: {
    data: [],
    isLoading: false,
    error: "",
    pagination: 0,
  },

  actions: {
    startLoading: () => {
      guidesAtom.change("isLoading", true);
    },

    endLoading() {
      guidesAtom.change("isLoading", false);
    },

    failed: (error: string) => {
      guidesAtom.change("error", error);
    },

    success: (data: IGuideItem[], pagination?: number) => {
      guidesAtom.merge({
        data,
        pagination,
      });
    },
  },
});
