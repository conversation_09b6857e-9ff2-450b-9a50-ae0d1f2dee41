import { useEffect } from "react";
import { referencesAtom } from "../../atoms/references-atom";
import { getAllReferences } from "../../services/get-all-references";
import { Link } from "react-router";
import { TbWorldShare } from "react-icons/tb";
import { IReferenceItem } from "../../types";
import { useTranslation } from "react-i18next";
import i18n from "../../locales/i18n";

// Reference Item Component
function ReferenceItem({ title, website }: IReferenceItem) {
  return (
    <Link
      target="_blank"
      to={website}
      style={{
        display: "flex",
        alignItems: "center",
        gap: "2px",
        padding: "5px 10px",
        borderRadius: "5px",
        textDecoration: "none",
        color: "#53433F",
        transition: "all 0.3s ease",
        cursor: "pointer",
        fontSize: "14px",
        fontWeight: "700",
      }}
    >
      <TbWorldShare className="text-main_color_light" size={24} />
      {title}
    </Link>
  );
}

export default function ReferencesContent() {
  const { error, isLoading, references } = referencesAtom.useValue();
  const { t } = useTranslation();

  useEffect(() => {
    getAllReferences();
  }, []);

  if (isLoading) {
    return (
      <div
        style={{
          textAlign: "center",
          fontSize: "20px",
          marginBlock: "100px",
        }}
      >
        {i18n.language === "ar" ? "جاري التحميل..." : "Loading..."}
      </div>
    );
  }

  if (error) {
    return (
      <div
        style={{
          textAlign: "center",
          fontSize: "20px",
          marginBlock: "100px",
          color: "red",
        }}
      >
        {error}
      </div>
    );
  }

  if (references.length === 0) {
    return (
      <div className="text-center py-5">
        {t("noReferencesFound", "No references found")}
      </div>
    );
  }

  return (
    <section
      className="references-section"
      style={{
        marginBlock: "10px",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "10px",
          padding: "16px",
        }}
      >
        {references.map((ref) => {
          return <ReferenceItem key={ref.id} {...ref} />;
        })}
      </div>
    </section>
  );
}
