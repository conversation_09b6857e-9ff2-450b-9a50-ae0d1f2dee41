import { useEffect, useRef, useContext } from "react";
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import esriRequest from "@arcgis/core/request";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import { MapContext } from "./MapContext";
import { addBaseMapLayer, addFeatureLayer } from "./MapUtils";

import MapImageLayer from "@arcgis/core/layers/MapImageLayer";
import { locale_atom } from "../../atoms/locale-atom";
declare global {
  interface Window {
    mapUrl: {
      ar: string;
      en: string;
    };
  }
}

let map: any;
let mapView: any;
let mapServiceUrl = locale_atom.currentValue == "ar"  ? window.mapUrl.ar : window.mapUrl.en;
const baseMapLayers: string[] = [];
const featureLayers: string[] = [
  "PROTECTED_AREA_BOUNDARY",
  "ECO_SYSTEM_MARINE",
  "ECO_SYSTEM_TERRESTIAL",
];

const MapComponent = () => {
  const mapRef = useRef(null);
  // const { setMap, setMapView, setMapInfo } = useContext(MapContext);
  const map_context = useContext(MapContext);

  useEffect(() => {
    console.log("mounting map component");
    map = new Map({
      basemap: "satellite",
    });

    mapView = new MapView({
      container: mapRef.current!,
      map: map,
      ui: {
        components: ["attribution"],
      },
      padding: {
        left: 500, // Same value as the #sidebar width in CSS
      },
      // extent: new Extent({
      //   xmin: 4785834.7959298305,
      //   ymin: 2933732.235282638,
      //   xmax: 5944008.648506764,
      //   ymax: 3353830.1427379455,
      //   spatialReference: {
      //     wkid: 102100,
      //     latestWkid: 3857,
      //   },
      // }),
    });
    mapView.ui._removeComponents(["attribution"]);

    mapView.on("key-down", (event: any) => {
      const prohibitedKeys = ["+", "-", "Shift", "_", "="];
      const keyPressed = event.key;
      if (prohibitedKeys.indexOf(keyPressed) !== -1) {
        event.stopPropagation();
      }
    });
    mapView.on("mouse-wheel", (event: any) => {
      event.stopPropagation();
    });
    mapView.on("double-click", (event: any) => {
      event.stopPropagation();
    });
    mapView.on("drag", (event: any) => {
      event.stopPropagation();
    });

    mapView.when(() => {

      var dynamicMapServiceLayer = new MapImageLayer({
        url: mapServiceUrl,
        id: "baseMap",
        sublayers: [
          {
            id: 3, //WaterMark
            visible: true
          },
          {
            id: 4, //Region
            visible: true
          }
        ]
      });

      dynamicMapServiceLayer.load().then(() => {
        /*editingLayers.forEach((layer) => {
          if (layer_parent.allSublayers.items.find((x: Sublayer) => x.title == layer))
            layer_parent.allSublayers.items.find((x: Sublayer) => x.title == layer).visible = false;
        });*/
        
        mapView.constraints = {
          // Disable zooming
          minZoom: mapView.zoom,
          maxZoom: mapView.zoom,
        };
      });

      map.add(dynamicMapServiceLayer);

    });

    let highlightGraphicLayer = new GraphicsLayer({
      id: "highlightGraphicLayer",
    });
    map.add(highlightGraphicLayer);

    map_context?.setMap(map);
    map_context?.setMapView(mapView);

    esriRequest(mapServiceUrl, {
      query: { f: "json" },
      responseType: "json",
    }).then((response) => {
      map_context?.setMapInfo(response.data);
      const layers = response.data.layers;
      layers.forEach(async (layer: any) => {
        if (baseMapLayers.includes(layer.name)) {
          addBaseMapLayer(map, {
            url: `${mapServiceUrl}/${layer.id}`,
            title: layer.name,
          });
        }
      });
      layers.forEach(async (layer: any) => {
        if (featureLayers.includes(layer.name)) {
          addFeatureLayer(map, {
            url: `${mapServiceUrl}/${layer.id}`,
            title: layer.name,
          });
        }
      });
      const fullExtent = response.data.fullExtent;
      if (fullExtent) {
        mapView.extent = fullExtent;
      }
    });

    return () => {
      if (mapView) {
        mapView.destroy();
      }
    };
  }, []);

  return <div ref={mapRef} style={{ height: "600px", width: "1000px" }}></div>;
};

export { map, mapView, MapComponent };
