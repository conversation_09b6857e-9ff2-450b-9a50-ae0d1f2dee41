import Breadcrumb from "../components/Breadcrumb";
import page_bg from "../assets/images/page_bgg.png";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import AppsList from "../components/apps/AppsList";
import { useEffect } from "react";
import { useNavigate } from "react-router";

export default function AppsPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const isAuthenticated = localStorage.getItem("user");

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
    }
    // Robust scroll to top
    if (typeof window !== "undefined") {
      window.scrollTo({ top: 0, left: 0, behavior: "auto" });
    }
  }, []);

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.8 } },
  };
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <div className="e-services">
      <Breadcrumb>
        <h2>{t("apps.breadcrumb", "My Apps")}</h2>
      </Breadcrumb>

      <motion.div
        className="bg"
        initial="hidden"
        animate="visible"
        variants={fadeIn}
      >
        <img src={page_bg} alt="Map Vector" />
      </motion.div>

      <div className="container-fluid">
        <motion.div
          className="section-header flex-column align-items-start"
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
        >
          <motion.h5 className="section-title" variants={fadeInUp}>
            {t("apps.title", "My Apps")}{" "}
            <span className="colored">
              {t("apps.titleHighlight", "Portal")}
            </span>
          </motion.h5>
          <motion.p
            className="section-description"
            variants={fadeInUp}
            transition={{ delay: 0.2 }}
          >
            {t(
              "apps.description",
              "Browse and access your available applications."
            )}
          </motion.p>
        </motion.div>
      </div>

      <div className="container-fluid services-list">
        <div className="row g-3 align-items-center mb-4">
          <div className="col-12">
            <AppsList />
          </div>
        </div>
      </div>
    </div>
  );
}
