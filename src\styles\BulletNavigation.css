/* Bullet Navigation Styles - Similar to NCVC.gov.sa */
.bullet-navigation {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  pointer-events: none;
  user-select: none;
}

.bullet-navigation-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  pointer-events: auto;
}

.bullet-item {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 8px;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.bullet-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(-5px);
}

.bullet-item.active {
  background: rgba(180, 84, 52, 0.8);
  border-color: rgba(180, 84, 52, 1);
  transform: translateX(-10px);
}

.bullet-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.7);
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 12px;
}

.bullet-item.active .bullet-dot {
  background: #fff;
  width: 16px;
  height: 16px;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.bullet-icon {
  font-size: 8px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bullet-item.active .bullet-icon {
  color: #b45434;
  font-size: 10px;
}

.bullet-label {
  margin-left: 12px;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  white-space: nowrap;
  pointer-events: none;
}

.bullet-item:hover .bullet-label,
.bullet-item.active .bullet-label {
  opacity: 1;
  transform: translateX(0);
}

.bullet-label span {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.bullet-line {
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.bullet-item:hover .bullet-line,
.bullet-item.active .bullet-line {
  width: 20px;
}

.bullet-item.active .bullet-line {
  background: #fff;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Full screen section styling */
.full-screen-section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

/* RTL Support */
[dir="rtl"] .bullet-navigation {
  right: auto;
  left: 30px;
}

[dir="rtl"] .bullet-item:hover {
  transform: translateX(5px);
}

[dir="rtl"] .bullet-item.active {
  transform: translateX(10px);
}

[dir="rtl"] .bullet-label {
  margin-left: 0;
  margin-right: 12px;
  transform: translateX(10px);
}

[dir="rtl"] .bullet-item:hover .bullet-label,
[dir="rtl"] .bullet-item.active .bullet-label {
  transform: translateX(0);
}

[dir="rtl"] .bullet-line {
  right: auto;
  left: -15px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .bullet-navigation {
    right: 20px;
  }

  [dir="rtl"] .bullet-navigation {
    left: 20px;
  }
}

@media (max-width: 768px) {
  .bullet-navigation {
    right: 15px;
    transform: translateY(-50%) scale(0.9);
  }

  [dir="rtl"] .bullet-navigation {
    left: 15px;
  }

  .bullet-navigation-container {
    gap: 15px;
  }

  .bullet-item {
    padding: 6px;
  }

  .bullet-label span {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .bullet-navigation {
    right: 10px;
    transform: translateY(-50%) scale(0.8);
  }

  [dir="rtl"] .bullet-navigation {
    left: 10px;
  }

  .bullet-navigation-container {
    gap: 12px;
  }

  .bullet-label {
    display: none; /* Hide labels on very small screens */
  }
}

/* Animation for smooth transitions */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(180, 84, 52, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(180, 84, 52, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(180, 84, 52, 0);
  }
}

.bullet-item.active {
  animation: pulse 2s infinite;
}

/* Accessibility */
.bullet-item:focus {
  outline: 2px solid rgba(180, 84, 52, 0.8);
  outline-offset: 2px;
}

.bullet-item:focus-visible {
  outline: 2px solid rgba(180, 84, 52, 0.8);
  outline-offset: 2px;
}
