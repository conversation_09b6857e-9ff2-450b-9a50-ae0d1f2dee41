@keyframes zoomInOut {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes panAndZoom {
  0% {
    transform: scale(1) translateX(0) translateY(0);
  }
  25% {
    transform: scale(1.05) translateX(-2%) translateY(-1%);
  }
  50% {
    transform: scale(1.1) translateX(0) translateY(0);
  }
  75% {
    transform: scale(1.05) translateX(2%) translateY(1%);
  }
  100% {
    transform: scale(1) translateX(0) translateY(0);
  }
}

@keyframes slowZoom {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.08);
  }
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes headerGlow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.9;
  }
}

.ncw-map .header-container {
  animation: headerGlow 4s ease-in-out infinite;
}

.ncw-map .environmental-card-scroll::-webkit-scrollbar {
  width: 4px;
}

.ncw-map .environmental-card-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.ncw-map .environmental-card-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(
    180deg,
    rgba(127, 205, 255, 0.6) 0%,
    rgba(180, 84, 52, 0.6) 100%
  );
  border-radius: 2px;
  transition: all 0.3s ease;
}

.ncw-map .environmental-card-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    180deg,
    rgba(127, 205, 255, 0.8) 0%,
    rgba(180, 84, 52, 0.8) 100%
  );
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(127, 205, 255, 0.5), 0 0 8px 2px rgba(0, 0, 0, 0.2);
  }
  70% {
    box-shadow: 0 0 0 12px rgba(127, 205, 255, 0),
      0 0 8px 2px rgba(0, 0, 0, 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(127, 205, 255, 0.5), 0 0 8px 2px rgba(0, 0, 0, 0.2);
  }
}
