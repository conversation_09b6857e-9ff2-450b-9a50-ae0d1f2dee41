import endpoint from "../api/endpoint";
import { allGuideListAtom } from "../atoms/guides/all-guide-list-atom";
import { oneGuideListAtom } from "../atoms/guides/one-guide-list-atom";
import { IGetOneGuideListResponse } from "../types";

export const getOneGuideList = async (
  id: number,
  page: number,
  search?: string
) => {
  try {
    /***
     * start loading
     */
    oneGuideListAtom.startLoading();

    /***
     * fetch data
     */
    const { data } = await endpoint.get<IGetOneGuideListResponse>(
      `/Organism/GetAll`,
      {
        params: {
          q: search,
          // filter_key: i18n.language === "ar" ? "name" : "name_en",
          filter_key: "name",
          contain: 1,
          classification_id: id,
          page,
        },
      }
    );

    /***
     * set data, pagination
     */
    oneGuideListAtom.success(data.results, data.totalPages);

    // allGuideListAtom.update((oldValue) => {
    //   return [...oldValue, ...data.results];
    // });

    allGuideListAtom.update(() => {
      return [...data.results];
    });

    /***
     * end loading
     */
    oneGuideListAtom.endLoading();
  } catch (error) {
    /***
     * set error
     */
    oneGuideListAtom.failed((error as Error).message);

    /***
     * end loading
     */
    oneGuideListAtom.endLoading();
  }
};
