import React from "react";

interface MapImageProps {
  src: string;
  alt: string;
  isTransitioning: boolean;
}

const MapImage: React.FC<MapImageProps> = ({ src, alt, isTransitioning }) => (
  <img
    src={src}
    alt={alt}
    // loading="lazy"
    style={{
      maxWidth: "98%",
      // width : "100%",
      // objectFit :"contain",
      maxHeight: 700,
      position: "relative",
      zIndex: -1,
      transition: "all 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
      opacity: isTransitioning ? 0.4 : 1,
      transform: isTransitioning ? "scale(1.05)" : "scale(1)",
      transformOrigin: "center center",
      filter: isTransitioning
        ? "brightness(0.7) contrast(1.2)"
        : "brightness(1) contrast(1)",
    }}
  />
);

export default MapImage;
