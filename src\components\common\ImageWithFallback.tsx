import { useState, useEffect } from "react";
import placeholderImage from "../../assets/images/placeholder.svg";

interface ImageWithFallbackProps {
  src: string;
  alt?: string;
  className?: string;
  style?: React.CSSProperties;
}

const ImageWithFallback: React.FC<ImageWithFallbackProps> = ({
  src,
  alt = "",
  className = "",
  style = {},
}) => {
  const [imgSrc, setImgSrc] = useState<string>(src);
  const [imgLoaded, setImgLoaded] = useState<boolean>(false);
  const [imgError, setImgError] = useState<boolean>(false);

  console.log(imgLoaded);

  useEffect(() => {
    setImgSrc(src);
    setImgError(false);
    setImgLoaded(false);
  }, [src]);

  const onError = () => {
    if (!imgError) {
      setImgSrc(placeholderImage);
      setImgError(true);
    }
  };

  const onLoad = () => {
    setImgLoaded(true);
  };

  return (
    <img
      src={imgSrc}
      alt={alt}
      className={className}
      style={style}
      onError={onError}
      onLoad={onLoad}
    />
  );
};

export default ImageWithFallback;
