import { CiSearch } from "react-icons/ci";
import { getOneGuideList } from "../../services/get-one-guide-list";
import { selectedGuideAtom } from "../../atoms/guides/selected-guide";
import i18n from "../../locales/i18n";

export default function OneGuideListSearch() {
  const { guide } = selectedGuideAtom.useValue();

  if (!guide) {
    return;
  }

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <div style={{ fontSize: "40px", fontWeight: "bold" }}>
        {i18n.language === "ar" ? guide?.name : guide.name_en}
      </div>

      <div className="search-container">
        <input
          type="search"
          placeholder={i18n.language === "ar" ? "بحث" : "Search"}
          onChange={(e) => {
            getOneGuideList(Number(guide?.id), 0, e.target.value);
          }}
          className="search-input"
        />
        <CiSearch />
      </div>
    </div>
  );
}
