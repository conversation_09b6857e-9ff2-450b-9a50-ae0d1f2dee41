window.__config = {
  prod: {
    // ApiUrl: "https://gisportal.syadtech.com/WildlifeAPI/",
    // ImageUrl: "https://gisportal.syadtech.com/wildlifefiles/",
    wildlifeExplorer: "http://localhost:3000/wildlifeexplorer",
    mapUrl:
    {
      ar: "https://geoservices2.syadtech.com/server/rest/services/AppGeoportal/WildLifeGeoportalViewAR/MapServer",
      en: "https://geoservices2.syadtech.com/server/rest/services/AppGeoportal/WildLifeGeoportalViewEN/MapServer"
    },
    domain: "https://geoservices2.syadtech.com",
    apiUrl: "https://geoservices2.syadtech.com/wildlifestgapi",
    ncw: "https://www.ncw.gov.sa/",
    vision_2030: "https://www.vision2030.gov.sa",
    requestOpenData:
      "https://open.data.gov.sa/ar/publishers/b39db5e4-dd11-46c2-8fe9-a436e9b21b3d/request-datasets",
    ssoUrl: "https://www.ncw.gov.sa/",
    checkInternal: "https://geoservices2.syadtech.com/wildlifeapi/health/live"
  },
  stage: {
    // ApiUrl: "https://geoservices2.syadtech.com/wildlifestgapi/",
    // ImageUrl: "https://geoservices2.syadtech.com/wildlifefiles/",
    wildlifeExplorer: "http://localhost:3000/wildlifeexplorer",
    mapUrl:
    {
      ar: "https://geoservices2.syadtech.com/server/rest/services/AppGeoportal/WildLifeGeoportalViewAR/MapServer",
      en: "https://geoservices2.syadtech.com/server/rest/services/AppGeoportal/WildLifeGeoportalViewEN/MapServer"
    },
    domain: "https://geoservices2.syadtech.com",
    apiUrl: "https://geoservices2.syadtech.com/wildlifestgapi",
    ncw: "https://www.ncw.gov.sa/",
    vision_2030: "https://www.vision2030.gov.sa",
    requestOpenData:
      "https://open.data.gov.sa/ar/publishers/b39db5e4-dd11-46c2-8fe9-a436e9b21b3d/request-datasets",
    ssoUrl: "https://www.ncw.gov.sa/",
    checkInternal: "https://geoservices2.syadtech.com/wildlifeapi/health/live"
  },
  master: {
    // ApiUrl: "https://geoservices2.syadtech.com/WildlifeAPI/",
    // ImageUrl: "https://geoservices2.syadtech.com/wildlifefiles/",
    wildlifeExplorer: "http://localhost:3000/wildlifeexplorer",
    mapUrl:
    {
      ar: "https://geoservices2.syadtech.com/server/rest/services/AppGeoportal/WildLifeGeoportalViewAR/MapServer",
      en: "https://geoservices2.syadtech.com/server/rest/services/AppGeoportal/WildLifeGeoportalViewEN/MapServer"
    },
    domain: "https://geoservices2.syadtech.com",
    apiUrl: "https://geoservices2.syadtech.com/wildlifestgapi",
    ncw: "https://www.ncw.gov.sa/",
    vision_2030: "https://www.vision2030.gov.sa",
    requestOpenData:
      "https://open.data.gov.sa/ar/publishers/b39db5e4-dd11-46c2-8fe9-a436e9b21b3d/request-datasets",
    ssoUrl: "https://www.ncw.gov.sa/",
    checkInternal: "https://geoservices2.syadtech.com/wildlifeapi/health/live",
    filesURL: "https://geoservices2.syadtech.com/wildlifefiles/",
  },
};
