import React from "react";

interface EnvironmentalRegionsOverlayProps {
  points: any[];
  activePoint: number | null;
  hoveredPoint: number | null;
  setHoveredPoint: (idx: number | null) => void;
  handlePointClick: (idx: number) => void;
}

const EnvironmentalRegionsOverlay: React.FC<
  EnvironmentalRegionsOverlayProps
> = ({
  points,
  activePoint,
  hoveredPoint,
  setHoveredPoint,
  handlePointClick,
}) => (
  <>
    {/* Points positioned relative to the map image */}
    {points.map((point, index) => (
      <div
        key={point.id}
        style={{
          position: "absolute",
          left: point.left,
          top: point.top,
          width: activePoint === index ? 16 : 18,
          height: activePoint === index ? 16 : 18,
          background: activePoint === index ? "#b45434" : "#fff",
          borderRadius: "50%",
          boxShadow:
            activePoint === index
              ? "0 0 15px 4px rgba(180, 84, 52, 0.6)"
              : "0 0 8px 2px rgba(0,0,0,0.2)",
          border:
            activePoint === index ? "3px solid #fff" : "3px solid #7fcdff",
          cursor: "pointer",
          pointerEvents: "auto",
          transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
          zIndex: activePoint === index ? 5 : 4,
          transform: activePoint === index ? "scale(1.1)" : "scale(1)",
          animation: "pulse 1.5s infinite cubic-bezier(0.4, 0, 0.2, 1)",
        }}
        onClick={() => handlePointClick(index)}
        onMouseEnter={() => setHoveredPoint(index)}
        onMouseLeave={() => setHoveredPoint(null)}
      />
    ))}

    {/* Tooltips */}
    {points.map(
      (point, index) =>
        hoveredPoint === index && (
          <div
            key={`tooltip-${point.id}`}
            style={{
              position: "absolute",
              left: `calc(${point.left} + 20px)`,
              top: `calc(${point.top} - 15px)`,
              background: "rgba(0, 0, 0, 0.5)",
              color: "#fff",
              padding: "6px 10px",
              borderRadius: "6px",
              fontSize: "13px",
              maxWidth: "fit-content",
              zIndex: 10,
              pointerEvents: "none",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
              animation: "tooltipFadeIn 0.3s ease-out",
            }}
          >
            <div
              style={{
                fontWeight: "bold",
                fontSize: "13px",
                color: "#7fcdff",
                whiteSpace: "nowrap",
              }}
            >
              {point.name}
            </div>
            <div
              style={{
                position: "absolute",
                left: "-5px",
                top: "50%",
                transform: "translateY(-50%)",
                width: 0,
                height: 0,
                borderTop: "5px solid transparent",
                borderBottom: "5px solid transparent",
                borderRight: "5px solid rgba(0, 0, 0, 0.5)",
              }}
            />
          </div>
        )
    )}
  </>
);

export default EnvironmentalRegionsOverlay;
