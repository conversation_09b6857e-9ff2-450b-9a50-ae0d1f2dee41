import { ReactNode } from "react";
import download_icon from "../../assets/images/e-services/download.svg";
import video_icon from "../../assets/images/e-services/video.svg";

interface DownloadCardProps {
  title: string;
  children?: ReactNode;
  className?: string;
}

/**
 * A reusable download card component for service pages
 */
const DownloadCard: React.FC<DownloadCardProps> = ({
  title,
  children,
  className = "",
}) => {
  return (
    <div className={`download-card ${className}`}>
      <span>{title}</span>

      <div>
        <img src={download_icon} alt="Download Guide" />
        <img src={video_icon} alt="Watch Video" />
      </div>

      {children}
    </div>
  );
};

export default DownloadCard;
