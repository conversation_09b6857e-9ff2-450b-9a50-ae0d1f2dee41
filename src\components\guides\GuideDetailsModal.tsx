import { oneGuideListAtom } from "../../atoms/guides/one-guide-list-atom";
import { selectedGuideIdAtom } from "../../atoms/guides/selected-guide-id-atom";
import ImageGallery from "react-image-gallery";
import { Modal } from "react-bootstrap";
import { useState, useEffect } from "react";

const placeholderImage =
  "https://geoservices2.syadtech.com/wildlifefiles/uploads/Mammals/Buhidae-sp/1.jpg";

/***
 * react image gallery
 */
import "react-image-gallery/styles/css/image-gallery.css";
import { Link } from "react-router";
import { TbWorldShare } from "react-icons/tb";
import i18n from "../../locales/i18n";

export default function GuideDetailsModal() {
  const [show, setShow] = useState(false);
  const { guideId } = selectedGuideIdAtom.useValue();

  /***
   * get one guide list
   */
  const { data } = oneGuideListAtom.useValue();

  /***
   * get one guide
   */
  const guide = data?.find((ele) => ele.id === guideId);

  // Show modal when guideId changes
  useEffect(() => {
    if (guideId) {
      setShow(true);
    }
  }, [guideId]);

  const handleClose = () => {
    setShow(false);
    selectedGuideIdAtom.change("guideId", null);
  };

  const images_list = guide?.media_path
    ? guide.media_path.split(",").map((ele) => {
        if (ele.startsWith("http")) {
          return ele;
        }
        return `${window.domain}/wildlifefiles/${ele}`;
      })
    : [placeholderImage]; // Use placeholder if no media_path

  const images = images_list.map((item) => {
    return {
      original: item,
      thumbnail: item,
      originalAlt: guide?.name || "Wildlife image",
      thumbnailAlt: guide?.name || "Wildlife thumbnail",
      thumbnailOnError: () => ({ thumbnail: placeholderImage }),
      originalOnError: () => ({ original: placeholderImage }),
      renderItem: (item: any) => (
        <div className="image-gallery-image">
          <img
            src={item.original}
            alt={item.originalAlt}
            onError={(e) => {
              e.currentTarget.onerror = null;
              e.currentTarget.src = placeholderImage;
            }}
          />
        </div>
      ),
      renderThumbInner: (item: any) => (
        <div className="image-gallery-thumbnail-inner">
          <img
            src={item.thumbnail}
            alt={item.thumbnailAlt}
            onError={(e) => {
              e.currentTarget.onerror = null;
              e.currentTarget.src = placeholderImage;
            }}
            className="image-gallery-thumbnail-image"
          />
        </div>
      ),
    };
  });

  return (
    <Modal
      className="guide-modal"
      show={show}
      onHide={handleClose}
      size="lg"
      centered
    >
      <Modal.Header closeButton></Modal.Header>
      <Modal.Body dir="rtl">
        <h2>{guide?.name}</h2>
        {/* <span style={{ color: "#B45434", fontWeight: "bold" }}>
          ثيللا - غبار - جدة
        </span> */}
        <ImageGallery
          items={
            images.length > 0
              ? images
              : [
                  {
                    original: placeholderImage,
                    thumbnail: placeholderImage,
                    originalAlt: "Placeholder image",
                    thumbnailAlt: "Placeholder thumbnail",
                  },
                ]
          }
          showPlayButton={false}
          showFullscreenButton={true}
          showBullets={false}
          showThumbnails={true}
          lazyLoad={true}
          onErrorImageURL={placeholderImage}
        />
        <div>{i18n.language === "ar" ? guide?.brief : guide?.brief_en}</div>

        <Link
          to={guide?.reference_url || "#"}
          // className="flex items-center gap-2 text-[14px] text-[#707070]"
          style={{
            fontSize: "14px",
            display: "flex",
            alignItems: "center",
            gap: "5px",
            color: "#b45434",
            marginTop: "10px",
          }}
          target="_blank"
        >
          <TbWorldShare />
          {guide?.reference_title}
        </Link>
      </Modal.Body>
    </Modal>
  );
}
