import cache from "@mongez/cache";
import { atom } from "@mongez/react-atom";
import i18n from "../locales/i18n";

type LocaleCodeAtom = "ar" | "en";

type LocaleCodeAtomActions = {
  toggleLanguage: () => void;
};

export const locale_atom = atom<LocaleCodeAtom, LocaleCodeAtomActions>({
  key: "locale-atom",
  default: cache.get("locale", "ar"),
  beforeUpdate: (newValue) => {
    /***
     * set locale in cache
     */
    cache.set("locale", newValue);

    /***
     * change language
     */
    i18n.changeLanguage(newValue);

    return newValue;
  },
  actions: {
    /**
     * toggle language
     */
    toggleLanguage() {
      locale_atom.update(locale_atom.value === "ar" ? "en" : "ar");
    },
  },
});
