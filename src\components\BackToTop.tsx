import { useState, useEffect } from "react";
import Tippy from "@tippyjs/react";
import "tippy.js/dist/tippy.css";

export default function BackToTop() {
  const [isVisible, setIsVisible] = useState(false);

  // Show button when scrolling down
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 1000) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);

    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  // Scroll to top when button is clicked
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <Tippy
      content="الرجوع لأعلى"
      placement="top"
      arrow={true}
      className="primary-tooltip"
      // visible={isVisible}
    >
      <a
        className={`back-top-btn ${isVisible ? "visible" : "hidden"}`}
        onClick={scrollToTop}
        style={{ cursor: "pointer" }}
      >
        <svg width="16.198" height="14.681" viewBox="0 0 16.198 14.681">
          <path
            id="back-to-top"
            d="M-12770.516,233.668l-6.984,5.962-.5-.427,7.484-6.388,7.481,6.388-.5.427Zm0-6.815-6.984,5.962-.5-.427,7.484-6.388,7.481,6.388-.5.427Z"
            transform="translate(12778.616 -225.474)"
            fill="none"
            stroke="#fff"
            strokeWidth="0.8"
          />
        </svg>
      </a>
    </Tippy>
  );
}
