// import i18n from "@/locales/i18n";

// /***
//  * handle scroll to top
//  */
export const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};

// /***
//  * format number
//  */
// export const formatNumber = (number: number) => {
//   const language = i18n.language;
//   const format = language === "en" ? "en-US" : "ar-EG";

//   return Intl.NumberFormat(format).format(number);
// };

// /***
//  * format price
//  */
// export const formatPrice = (price: number) => {
//   const language = i18n.language;
//   const format = language === "en" ? "en-US" : "ar-EG";
//   const currency = language === "en" ? "USD" : "EGP";

//   return new Intl.NumberFormat(format, {
//     style: "currency",
//     currency: currency,
//   }).format(price);
// };

// /***
//  * to handle scroll spy
//  */
// // export const handleScrollSpy = (sections: ISection[]) => {
// //   const scrollPosition = window.scrollY + window.innerHeight / 2;
// //   sections.forEach((section) => {
// //     const element = document.getElementById(section.id)!;
// //     if (
// //       element.offsetTop <= scrollPosition &&
// //       element.offsetTop + element.offsetHeight > scrollPosition
// //     ) {
// //       activeSectionAtom.update(section.id);
// //     }
// //   });

// //   // useEffect(() => {
// //   //   window.addEventListener("scroll", () => handleScrollSpy(sections));
// //   //   return () => {
// //   //     window.removeEventListener("scroll", () => handleScrollSpy(sections));
// //   //   };
// //   // }, []);
// // };
