.login-page {
  padding: 50px 0;
  position: relative;
}

.login-bg {
  position: absolute;
  inset: 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: left;
  mix-blend-mode: exclusion;
  opacity: 20%;
  /* mix-blend-mode: multiply;
  opacity: 90%; */
  z-index: 3;
}

.login-wave {
  position: absolute;
  inset: 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  mix-blend-mode: multiply;
  opacity: 10%;
  z-index: 2;
}

/* .overlay {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background-color: #252829;
  z-index: 1;
} */

.container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  z-index: 5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.login-container {
  display: flex;
  justify-content: space-between;
  text-align: center;
  gap: 20px;
  /* margin-bottom: 70px; */
  padding: 40px;
  border-radius: 20px;
  background: linear-gradient(
    180deg,
    rgba(55, 63, 68, 0.4) 0%,
    rgba(74, 82, 90, 0.4) 50%,
    rgba(26, 54, 93, 0.4) 100%
  );
  width: 100%;
  max-width: 1200px;
}

.login-form {
  padding: 20px 40px 20px 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  justify-content: center;
}

@media (max-width: 992px) {
  .login-form {
    padding: 0;
  }
}

.login-title {
  font-size: 22px;
  font-weight: 700;
  color: #fff;
  margin: 0;
}

.login-subtitle {
  font-size: 16px;
  font-weight: 400;
  color: #ccd7e2;
  margin: 0;
}

.login-button {
  height: 50px;
  border-radius: 50px;
  width: 100%;
  font-weight: 700;
  font-size: 14px;
  color: #fff;
  background-color: transparent;
  border: 1px solid #fff;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.login-button-primary {
  background-color: #b45434;
  border: none;
}

.login-button-primary:hover {
  background-color: #9a4730;
}

.divider-container {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.divider-line {
  flex: 1;
  height: 1px;
  background-color: #606870;
}

.divider-text {
  font-size: 14px;
  color: #fff;
  font-weight: 700;
  padding: 0 10px;
}

.input-group {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
}

.login-input {
  height: 50px;
  border-radius: 50px !important;
  border: none;
  padding: 15px;
  background-color: #fff;
  width: 100%;
  font-size: 14px;
}

.login-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(180, 84, 52, 0.2);
}

.login-input.error {
  border: 1px solid #ff4d4f;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  display: block;
  text-align: right;
}

.remember-me-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: -10px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 28px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #606870;
  transition: 0.4s;
  border-radius: 28px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #b45434;
}

input:checked + .slider:before {
  transform: translateX(24px);
}

.remember-me-text {
  color: #a4a4a4;
  font-size: 14px;
}

.forgot-password-link {
  color: #b45434;
  font-size: 14px;
  font-weight: 600;
  text-decoration: underline;
  text-align: center;
}

.forgot-password-link:hover {
  color: #9a4730;
}

.form-divider {
  border-bottom: 1px solid #606870;
  width: 100%;
}

.create-account-link {
  color: #999999;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  text-decoration: none;
}

.create-account-link:hover {
  color: #666666;
}

.footer-text {
  color: #a9593c;
  font-weight: 700;
  font-size: 16px;
  text-align: center;
  margin-top: 20px;
}

.login-image-container {
  flex: 1;
  justify-content: end;
  align-items: center;
  display: none;
}

@media (min-width: 992px) {
  .login-image-container {
    display: flex;
  }
}

.login-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  max-width: 500px;
}

.password-toggle {
  position: absolute;
  top: 15px;
  inset-inline-end: 12px;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #333;
}

.password-toggle:focus {
  outline: none;
}

.password-toggle svg {
  width: 20px;
  height: 20px;
}
