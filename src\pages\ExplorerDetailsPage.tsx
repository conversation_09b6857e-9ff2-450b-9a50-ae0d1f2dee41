import { useTranslation } from "react-i18next";
import { urls } from "../utils/urls";
import ServiceDetailsLayout from "../components/layouts/ServiceDetailsLayout";
import SectionTitle from "../components/common/SectionTitle";
import BeneficiariesList from "../components/services/BeneficiariesList";
import ServiceTimeSection from "../components/services/ServiceTimeSection";

export default function ExplorerDetailsPage() {
  const { t } = useTranslation();

  return (
    <ServiceDetailsLayout
      breadcrumbTitle={t("explorerDetailsPage.breadcrumb")}
      downloadGuideText={t("explorerDetailsPage.downloadGuide")}
      downloadCardContent={
        <a
          href={urls.wildlifeExplorer}
          target="_blank"
          className="btn btn-outline-secondary"
        >
          {t("explorerDetailsPage.startService")}
        </a>
      }
    >
      <SectionTitle
        title={t("explorerDetailsPage.title")}
        description={t("explorerDetailsPage.description")}
      />

      <div>
        <SectionTitle
          title={t("explorerDetailsPage.procedures.title").split(" ")[0]}
          coloredPart={t("explorerDetailsPage.procedures.title").split(" ")[1]}
        />

        <ul className="section-list" style={{ width: "100%" }}>
          {(
            t("explorerDetailsPage.procedures.steps", {
              returnObjects: true,
            }) as string[]
          ).map((step: string, index: number) => (
            <li key={index} className="section-description">
              {step}
              {index === 3 && (
                <ul
                  className="section-list"
                  style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
                    width: "100%",
                  }}
                >
                  {(
                    t("explorerDetailsPage.procedures.features", {
                      returnObjects: true,
                    }) as string[]
                  ).map((feature: string, idx: number) => (
                    <li key={idx} className="section-description">
                      {feature}
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </div>

      <BeneficiariesList
        title={t("explorerDetailsPage.beneficiaries.title").split(" ")[0]}
        coloredPart={t("explorerDetailsPage.beneficiaries.title").split(" ")[1]}
        beneficiaries={
          t("explorerDetailsPage.beneficiaries.list", {
            returnObjects: true,
          }) as string[]
        }
      />

      {/* <div>
        <SectionTitle
          title={t("explorerDetailsPage.terms.title").split(" ")[0]}
          coloredPart={t("explorerDetailsPage.terms.title").split(" ")[1]}
          description={t("explorerDetailsPage.terms.description")}
        />
      </div> */}

      <ServiceTimeSection
        title={t("explorerDetailsPage.serviceTime.title").split(" ")[0]}
        coloredPart={t("explorerDetailsPage.serviceTime.title")
          .split(" ")
          .slice(1)
          .join(" ")}
        description={t("explorerDetailsPage.serviceTime.description")}
      />
    </ServiceDetailsLayout>
  );
}
