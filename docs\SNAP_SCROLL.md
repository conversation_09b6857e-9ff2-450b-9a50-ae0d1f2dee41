# Snap Scroll Implementation

This document describes the snap scroll functionality implemented in the Wildlife Portal project.

## Overview

The snap scroll feature provides a smooth, full-page scrolling experience similar to modern websites like NCVC.gov.sa. Users can navigate between sections using:

- Mouse wheel scrolling
- Touch gestures (mobile)
- Keyboard navigation (arrow keys, page up/down, home/end)
- Bullet navigation clicks

## Components

### 1. useSnapScroll Hook (`src/hooks/useSnapScroll.ts`)

A custom React hook that manages snap scroll behavior:

**Features:**
- Wheel event handling for desktop
- Touch gesture support for mobile
- Keyboard navigation
- Section change callbacks
- Automatic body class management

**Usage:**
```typescript
const { scrollToSection, scrollToNext, scrollToPrevious } = useSnapScroll({
  sectionIds: ['section1', 'section2', 'section3'],
  enabled: true,
  onSectionChange: (sectionId, index) => {
    console.log('Active section:', sectionId);
  }
});
```

### 2. BulletNavigation Component

Enhanced with snap scroll integration:

**New Props:**
- `enableSnapScroll?: boolean` - Enable/disable snap scroll functionality

**Usage:**
```tsx
<BulletNavigation 
  sections={bulletSections} 
  enableSnapScroll={true} 
/>
```

### 3. CSS Classes

**`.snap-scroll-section`** - Apply to sections that should snap:
```css
.snap-scroll-section {
  scroll-snap-align: start;
  scroll-snap-stop: always;
  min-height: 100vh;
}
```

**`.snap-scroll-enabled`** - Automatically applied to body when snap scroll is active

## Implementation in HomePage

```tsx
// Sections with snap scroll class
<section id="hero-section" className="snap-scroll-section">
  <Hero />
</section>

<section id="map-section" className="snap-scroll-section">
  <Map2 />
</section>

// Bullet navigation with snap scroll enabled
<BulletNavigation sections={bulletSections} enableSnapScroll={true} />
```

## Features

### Desktop Experience
- **Mouse Wheel**: Scroll between sections with mouse wheel
- **Keyboard**: Use arrow keys, page up/down, home/end for navigation
- **Smooth Transitions**: CSS-based smooth scrolling with momentum

### Mobile Experience
- **Touch Gestures**: Swipe up/down to navigate between sections
- **Dynamic Viewport**: Uses `100dvh` for proper mobile viewport handling
- **Touch Scrolling**: Optimized for mobile touch devices

### Visual Enhancements
- **Hidden Scrollbars**: Clean full-page experience without visible scrollbars
- **Bullet Navigation**: Real-time active section tracking
- **Smooth Animations**: Framer Motion integration for enhanced UX

## Browser Support

- **Modern Browsers**: Full CSS scroll-snap support
- **Mobile Safari**: Optimized touch handling
- **Firefox**: Custom scrollbar hiding
- **Edge/IE**: Fallback scrollbar styles

## Configuration

### Disable Snap Scroll
```tsx
<BulletNavigation sections={bulletSections} enableSnapScroll={false} />
```

### Custom Threshold
```typescript
useSnapScroll({
  sectionIds,
  threshold: 0.3, // Section becomes active when 30% visible
  enabled: true
});
```

## Performance Considerations

- **Throttled Events**: Scroll and wheel events are throttled using `requestAnimationFrame`
- **Passive Listeners**: Touch events use passive listeners for better performance
- **Cleanup**: Proper event listener cleanup on component unmount

## Accessibility

- **Keyboard Navigation**: Full keyboard support for accessibility
- **Focus Management**: Proper focus handling during navigation
- **Reduced Motion**: Respects user's motion preferences

## Troubleshooting

### Sections Not Snapping
- Ensure sections have `snap-scroll-section` class
- Check that `enableSnapScroll` is set to `true`
- Verify section IDs match the ones passed to `useSnapScroll`

### Mobile Issues
- Check viewport meta tag is properly configured
- Ensure touch events are not being prevented by other components
- Verify `overscroll-behavior` is set correctly

### Performance Issues
- Reduce the number of sections if experiencing lag
- Check for conflicting scroll event listeners
- Ensure proper cleanup of event listeners
