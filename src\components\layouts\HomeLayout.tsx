import { ReactNode } from "react";
import { motion } from "framer-motion";
import { fadeIn } from "../../utils/animations";

interface HomeLayoutProps {
  children: ReactNode;
  className?: string;
}

/**
 * A specialized layout component for the home page
 * This layout doesn't include the common elements like breadcrumb
 * that are present in other page layouts
 */
const HomeLayout: React.FC<HomeLayoutProps> = ({
  children,
  className = "",
}) => {
  return (
    <motion.div 
      className={className}
      initial="hidden"
      animate="visible"
      variants={fadeIn}
    >
      {children}
    </motion.div>
  );
};

export default HomeLayout;
