import { Link, useLocation } from "react-router";
import { Offcanvas } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import { locale_atom } from "../../atoms/locale-atom";

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function MobileMenu({ isOpen, onClose }: MobileMenuProps) {
  const { pathname } = useLocation();
  const { t } = useTranslation();
  const currentLocale = locale_atom.useValue();
  const isLoggedIn = localStorage.getItem("user");

  const handleClose = () => onClose();

  return (
    <>
      <Offcanvas dir="rtl" show={isOpen} onHide={onClose} placement="start">
        <div className="offcanvas-header">
          <h5 className="offcanvas-title" id="offcanvasExampleLabel">
            {currentLocale === "en" ? "Menu" : "القائمة"}
          </h5>
          <button
            type="button"
            className="btn-close"
            onClick={handleClose}
            aria-label="Close"
          ></button>
        </div>
        <div className="offcanvas-body">
          <ul className="navbar-nav">
            <li className="nav-item">
              <Link
                className={`nav-link ${pathname === "/" && "active"}`}
                aria-current="page"
                to="/"
                // data-bs-dismiss="offcanvas"
              >
                {t("navbar.home")}
              </Link>
            </li>
            <li className="nav-item">
              <Link
                className={`nav-link ${pathname === "/about" && "active"}`}
                to="/about"
                // data-bs-dismiss="offcanvas"
              >
                {t("navbar.about")}
              </Link>
            </li>
            <li className="nav-item">
              <Link
                className={`nav-link ${pathname === "/services" && "active"}`}
                to="/services"
                // data-bs-dismiss="offcanvas"
              >
                {t("navbar.services")}
              </Link>
            </li>
            {/* <li className="nav-item">
            <Link
              className="nav-link"
              to="#"
              data-bs-dismiss="offcanvas"
            >
              التسجيل الوطني للمحميات{" "}
            </Link>
          </li> */}
            <li className="nav-item">
              <Link
                className={`nav-link ${pathname === "/guides" && "active"}`}
                to="/guides"
                // data-bs-dismiss="offcanvas"
              >
                {t("navbar.guides")}
              </Link>
            </li>

            {isLoggedIn && (
              <li className="nav-item">
                <Link
                  className={`nav-link ${pathname === "/apps" && "active"}`}
                  to="/my-apps"
                >
                  {t("apps.breadcrumb", "My Apps")}
                </Link>
              </li>
            )}
          </ul>
        </div>
      </Offcanvas>
    </>
  );
}
