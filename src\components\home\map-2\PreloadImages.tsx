import React from "react";

interface PreloadImagesProps {
  pointImages: string[];
  noBorderImage: string;
  bgImages: string[];
  defaultBg: string;
}

const PreloadImages: React.FC<PreloadImagesProps> = ({
  pointImages,
  noBorderImage,
  bgImages,
  defaultBg,
}) => (
  <div style={{ display: "none" }}>
    {pointImages.map((img, idx) => (
      <img
        // loading="lazy"
        key={"point-" + idx}
        src={img}
        alt={`preload-point-${idx}`}
      />
    ))}
    <img src={noBorderImage} alt="preload-no-border" />
    {bgImages.map((bg, idx) => (
      <img
        // loading="lazy"
        key={"bg-" + idx}
        src={bg}
        alt={`preload-bg-${idx}`}
      />
    ))}
    <img
      src={defaultBg}
      // loading="lazy"
      alt="preload-bg-default"
    />
  </div>
);

export default PreloadImages;
