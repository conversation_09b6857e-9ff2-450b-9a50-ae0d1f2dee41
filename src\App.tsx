import { useEffect, Suspense, lazy } from "react";
import { Route, Routes } from "react-router";
// Lazy load all pages
const HomePage = lazy(() => import("./pages/HomePage"));
const NotFoundPage = lazy(() => import("./pages/NotFoundPage"));
const AboutPage = lazy(() => import("./pages/AboutPage"));
const ServicesPage = lazy(() => import("./pages/ServicesPage"));
const ExplorerDetailsPage = lazy(() => import("./pages/ExplorerDetailsPage"));
const GuidesPage = lazy(() => import("./pages/GuidesPage"));
const RassedDetailsPage = lazy(() => import("./pages/RassedDetailsPage"));
const RequestDataDetailsPage = lazy(
  () => import("./pages/RequestDataDetailsPage")
);
const MakeDecisionDetailsPage = lazy(
  () => import("./pages/MakeDecisionDetailsPage")
);
const LoginSso = lazy(() => import("./pages/LoginSso"));
const LoginPage = lazy(() => import("./pages/LoginPage"));
const AppsPage = lazy(() => import("./pages/AppsPage"));
import BackToTop from "./components/BackToTop";
import Loader from "./components/Loader";
import BaseLayout from "./components/layouts/BaseLayout";
import { Toaster } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { locale_atom } from "./atoms/locale-atom";

export default function App() {
  const { i18n } = useTranslation();
  const locale_code = locale_atom.useValue();

  useEffect(() => {
    i18n.changeLanguage(locale_code);
  }, []);

  return (
    <div dir={locale_code === "ar" ? "rtl" : "ltr"}>
      <Suspense fallback={<Loader />}>
        <Routes>
          <Route path="/" element={<BaseLayout />}>
            <Route path="/" element={<HomePage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/my-apps" element={<AppsPage />} />
            <Route path="/loginsso" element={<LoginSso />} />
            <Route path="/services" element={<ServicesPage />} />
            <Route
              path="/services/explorer"
              element={<ExplorerDetailsPage />}
            />
            <Route path="/services/rassed" element={<RassedDetailsPage />} />
            <Route
              path="/services/request-data"
              element={<RequestDataDetailsPage />}
            />
            <Route
              path="/services/make-decision"
              element={<MakeDecisionDetailsPage />}
            />
            <Route path="/guides" element={<GuidesPage />} />
          </Route>
          <Route path="/login" element={<LoginPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Suspense>
      <BackToTop />
      <Toaster position="top-center" reverseOrder={false} />
    </div>
  );
}
