import { Link } from "react-router-dom";
import { Application } from "../../types";
import i18n from "../../locales/i18n";

export default function AppItem({
  icon,
  translate_ar_caption,
  translate_en_caption,
  href,
}: Application) {
  return (
    <Link
      to={href && href.includes("?tk=") ? `${window.domain}${href}${localStorage.getItem("token")}` :`${window.domain}${href}` || "#"}
      className="app-card" target="_blank">
      <span className="app-icon">
        <img src={`${window.filesURL}/${icon}`} alt="icon" />
      </span>
      <span className="app-title">
        {i18n.language === "ar" ? translate_ar_caption : translate_en_caption}
      </span>
      <span className="app-description">
        {i18n.language === "ar" ? translate_ar_caption : translate_en_caption}
      </span>
    </Link>
  );
}
