import { useEffect, useState } from "react";
import AppItem from "./AppItem";
import { Application, User } from "../../types";
import { Row, Col } from "react-bootstrap";

export default function AppsList() {
  const [apps, setApps] = useState<Application[]>([]);

  useEffect(() => {
    // Get user applications from local storage
    const user: User = localStorage.getItem("user")
      ? JSON.parse(localStorage.getItem("user") as string)
      : null;

    if (user?.groups) {
      let userApplications = user.groups
        .map((x) => x.groups_permissions?.map((f) => f.applications) || [])
        .flat();

      // Filter out applications based on engineering company if needed
      if (user.engcompany_id && user.engineering_companies?.applications) {
        const deactivatedApps = user.engineering_companies.applications;
        userApplications = userApplications.filter(
          (app) => !deactivatedApps.find((deact: any) => deact.id === app.id)
        );
      }

      // Remove duplicates by id
      const uniqueApps = [
        ...new Map(userApplications.map((item) => [item.id, item])).values(),
      ];
      setApps(uniqueApps.filter((app) => app.href !== "empty"));
    }
  }, []);

  return (
    <Row className="py-4" style={{ rowGap: 28 }}>
      {apps.map((item) => (
        <Col
          key={item.id}
          xs={12}
          sm={6}
          md={4}
          lg={3}
          className="d-flex align-items-stretch mb-4"
        >
          <AppItem {...item} />
        </Col>
      ))}
    </Row>
  );
}
