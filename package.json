{"name": "portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@arcgis/core": "4.21", "@mongez/cache": "^1.2.4", "@mongez/react-atom": "^5.1.0", "@tippyjs/react": "^4.2.6", "apexcharts": "^4.6.0", "axios": "^1.8.4", "framer-motion": "^12.9.4", "i18next": "^25.0.1", "imagesloaded": "^5.0.0", "packery": "^3.0.0", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-bootstrap": "^2.10.9", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-image-gallery": "^1.4.0", "react-router": "^7.5.1", "react-router-dom": "^7.6.1", "swiper": "^11.2.6"}, "devDependencies": {"@eslint/js": "^9.25.1", "@types/imagesloaded": "^4.1.6", "@types/packery": "^1.4.37", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-image-gallery": "^1.2.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.25.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.31.1", "vite": "^6.3.1"}}