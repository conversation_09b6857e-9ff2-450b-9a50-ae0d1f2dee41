import Breadcrumb from "../components/Breadcrumb";
import page_bg from "../assets/images/page_bgg.png";

import Icon2 from "../assets/images/e-services/icon-2.svg";
import Icon1 from "../assets/images/e-services/icon-1.svg";
import Icon3 from "../assets/images/e-services/icon-3.svg";
import Icon4 from "../assets/images/e-services/icon-4.svg";
import Icon5 from "../assets/images/e-services/icon-5.svg";
import Icon6 from "../assets/images/e-services/icon-6.svg";
import Icon7 from "../assets/images/e-services/icon-7.svg";
import Icon8 from "../assets/images/e-services/icon-8.svg";
import Icon9 from "../assets/images/e-services/icon-9.svg";
import Icon10 from "../assets/images/e-services/icon-10.svg";
import Icon11 from "../assets/images/e-services/icon-11.svg";
import Icon12 from "../assets/images/e-services/icon-12.svg";

import { Link } from "react-router";
import { useEffect } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";

// Animation variants
const fadeIn = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.8 } },
};

const fadeInUp = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const cardVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut",
    },
  },
};

export default function ServicesPage() {
  const { t } = useTranslation();

  // Service icons array
  const serviceIcons = [
    Icon1, // Wildlife Rasid   Explorer
    Icon3, // Maps and Data Request
    Icon2, // Wildlife Monitor
    Icon4, // Decision Support
    Icon5, // Data Analysis
    Icon6, // Integration
    Icon7, // Open Data
    Icon8, // Data Update
    Icon9, // Documents
    Icon10, // Field Studies
    Icon11, // Violations in Protected Areas
    Icon12, // User Management
  ];

  // Service links array
  const serviceLinks = [
    "/services/explorer",
    "/services/request-data",
    "/services/rassed",
    "/services/make-decision",
    "#",
    "#",
    "#",
    "#",
    "#",
    "#",
    "#",
    "#",
  ];

  useEffect(() => {
    // Robust scroll to top
    if (typeof window !== "undefined") {
      window.scrollTo({ top: 0, left: 0, behavior: "auto" });
    }
  }, []);

  // Get service items from translations
  const serviceItems = t("services.serviceItems", {
    returnObjects: true,
  }) as Array<{
    name: string;
    text: string;
  }>;

  return (
    <div className="e-services">
      <Breadcrumb>
        <h2>{t("services.breadcrumb")}</h2>
      </Breadcrumb>

      <motion.div
        className="bg"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
        variants={fadeIn}
      >
        <img src={page_bg} alt="Map Vector" />
      </motion.div>

      <div className="container-fluid">
        <motion.div
          className="section-header flex-column align-items-start"
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
        >
          <motion.h5 className="section-title" variants={fadeInUp}>
            {t("services.title")}{" "}
            <span className="colored">{t("services.titleHighlight")}</span>
          </motion.h5>
          <motion.p
            className="section-description"
            variants={fadeInUp}
            transition={{ delay: 0.2 }}
          >
            {t("services.description")}
          </motion.p>
        </motion.div>
      </div>

      {/*  */}
      <div className="container-fluid services-list">
        <div className="row g-3 align-items-center mb-4">
          <div className="col-12">
            <motion.div
              className="row g-4"
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
            >
              {serviceItems.map((service, index) => {
                return (
                  <motion.div
                    className="col-12 col-md-6 col-lg-3"
                    key={index}
                    variants={cardVariant}
                    transition={{ delay: index * 0.05 }}
                  >
                    <Link
                      to={serviceLinks[index]}
                      style={{ height: "100%", display: "block" }}
                    >
                      <div className="services-card">
                        <span className="services-icon">
                          <img src={serviceIcons[index]} alt="" />
                        </span>
                        <span className="card-title">{service.name}</span>
                        <span className="services-title">{service.text}</span>
                      </div>
                    </Link>
                  </motion.div>
                );
              })}
            </motion.div>
          </div>
        </div>
      </div>
      {/*  */}
    </div>
  );
}
