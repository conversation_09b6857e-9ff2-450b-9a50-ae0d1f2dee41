import { atom } from "@mongez/react-atom";
import { User } from "../types";

interface AuthAtom {
  token: string | null;
  user: User | null;
}

interface AuthAtomActions {
  logout: () => void;
}

export const authAtom = atom<AuthAtom, AuthAtomActions>({
  key: "auth-atom",
  default: {
    token: localStorage.getItem("token"),
    user: JSON.parse(localStorage.getItem("user") as string) || null,
  },

  beforeUpdate(newValue) {
    if(newValue.token)
      localStorage.setItem("token", newValue.token);
    localStorage.setItem("user", JSON.stringify(newValue.user));

    return newValue;
  },

  actions: {
    logout() {
      authAtom.update({
        token: null,
        user: null,
      });

      localStorage.removeItem("token");
      localStorage.removeItem("user");
    },
  },
});
