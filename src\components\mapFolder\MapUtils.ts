import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import Graphic from "@arcgis/core/Graphic";

export function getFeatureLayerIdByName(mapInfo: any, layerName: any) {
  const sublayers = mapInfo.layers;

  for (const sublayer of sublayers) {
    if (sublayer.title === layerName || sublayer.name === layerName) {
      return sublayer.id;
    }
  }

  return null;
}

export async function addBaseMapLayer(map: any, { url, title }: any) {
  const existingLayer = map.layers.items.find((layer: any) => {
    return layer.id == title;
  });
  if (existingLayer) {
    console.log(
      "Layer already exists:",
      existingLayer.title || existingLayer.url
    );
  } else {
    const featureLayer = new FeatureLayer({
      url,
      title,
      id: title,
      visible: true,
    }) as FeatureLayer & { isBaseMap?: boolean };

    featureLayer.isBaseMap = true;

    try {
      map.add(featureLayer);
      return featureLayer;
    } catch (error) {
      console.error("Failed to load FeatureLayer:", error);
    }
  }
}

export async function addFeatureLayer(map: any, { url, title }: any) {
  const existingLayer = map.layers.items.find((layer: any) => {
    return layer.id == title;
  });
  if (existingLayer) {
    console.log(
      "Layer already exists:",
      existingLayer.title || existingLayer.url
    );
  } else {
    const featureLayer = new FeatureLayer({
      url,
      title,
      id: title,
      visible:
        title == "NCW_WATER_MARK" || "PROTECTED_AREA_BOUNDARY" ? true : false,
      definitionExpression:
        title == "PROTECTED_AREA_BOUNDARY"
          ? "EN_PROTECTED_AREA_STATUS = 'PA'"
          : "",
    });

    try {
      map.add(featureLayer);
      return featureLayer;
    } catch (error) {
      console.error("Failed to load FeatureLayer:", error);
    }
  }
}

export async function getFeaturesByUniqueValue(
  layer: any,
  fieldName: any,
  value: any
) {
  const query = layer.createQuery();
  if (Array.isArray(fieldName) && Array.isArray(value)) {
    let where = "";

    for (let index = 0; index < fieldName.length; index++) {
      const field = fieldName[index];
      const fieldValue = value[index];
      where = where + `${field} = '${fieldValue}' AND `;
    }
    where = where.slice(0, -4);
    query.where = where;
  } else {
    query.where = `${fieldName} = '${value}'`;
  }
  query.returnGeometry = true;
  const result = await layer.__layerView.queryFeatures(query);

  return result.features;
}

export function highlightFeatures(
  features: any,
  baseColor: any,
  graphicsLayer: any
) {
  graphicsLayer.removeAll();
  const darken = (value: any, factor = 0.6) =>
    Math.max(0, Math.floor(value * factor));
  const alpha = baseColor.a ?? 1;

  const fillColor = [
    darken(baseColor.r),
    darken(baseColor.g),
    darken(baseColor.b),
    alpha,
  ];

  console.log("fillColor", fillColor);
  //   const outlineColor = [baseColor.r, baseColor.g, baseColor.b, 1];
  const highlightOptions = {
    color: [180, 83, 51],
    fillOpacity: 1,
    haloOpacity: 1,
    haloColor: [0, 0, 0, 1],
  };

  const graphics = features.map((feature: any) => {
    return new Graphic({
      geometry: feature.geometry,
      attributes: feature.attributes,
      symbol: {
        // @ts-ignore
        type: "simple-fill",
        color: [...highlightOptions.color, highlightOptions.fillOpacity], // RGBA fill
        style: "solid",
        outline: {
          color: highlightOptions.haloColor,
          width: 1.5,
        },
      },
    });
  });

  graphicsLayer.addMany(graphics);
}
