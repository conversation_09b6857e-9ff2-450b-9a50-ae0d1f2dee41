import { useState } from "react";
import GuidesContent from "./GuidesContent";
import ReferencesContent from "./ReferencesContent";
import { useTranslation } from "react-i18next";

export default function GuidesTabs() {
  const [activeTab, setActiveTab] = useState<"guides" | "references">("guides");
  const { t } = useTranslation();

  const handleTabClick = (tab: "guides" | "references") => {
    setActiveTab(tab);
  };

  return (
    <>
      <div className="tabs">
        <div
          className={`tab ${activeTab === "guides" ? "active" : ""}`}
          onClick={() => handleTabClick("guides")}
        >
          {t("guidesTabs.guides")}
        </div>
        <div
          className={`tab ${activeTab === "references" ? "active" : ""}`}
          onClick={() => handleTabClick("references")}
        >
          {t("guidesTabs.references")}
        </div>
      </div>

      {activeTab === "guides" && (
        <div className="tab-content active">
          <GuidesContent />
        </div>
      )}

      {activeTab === "references" && (
        <div className="tab-content active">
          <ReferencesContent />
        </div>
      )}
    </>
  );
}
