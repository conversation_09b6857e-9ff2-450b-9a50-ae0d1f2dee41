// LoginPage.jsx
import { useLocation, useNavigate } from "react-router";
import endpoint from "../api/endpoint";
import toast from "react-hot-toast";
import { useState, useEffect } from "react";
import Loader from "../components/Loader";

const LoginSso = () => {
  const { search } = useLocation();
  const query = new URLSearchParams(search);
  const navigate = useNavigate();
  const code = query.get("code");

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const login = async () => {
      setIsLoading(true);
      try {
        const response = await endpoint.post("/sso-auth?code=" + code);
        localStorage.setItem("user", JSON.stringify(response.data));
        localStorage.setItem("token", response.data.token);

        if (localStorage.getItem("redirect")) {
          let redirect = localStorage.getItem("redirect");
          localStorage.removeItem("redirect");
          window.open(redirect || "/", "_self");
        } else {
          navigate("/my-apps");
        }
      } catch (error) {
        toast.error("حدث خطأ أثناء تسجيل الدخول يرجى المحاولة مرة أخرى");
          navigate("/login");
      } finally {
        setIsLoading(false);
      }
    };
    login();
    // eslint-disable-next-line
  }, [code, navigate]);

  if (isLoading) {
    return <Loader />;
  }

  return <></>;
};

export default LoginSso;
