import point_1 from "../../../assets/images/ncw-map/أشجار و مناطق الغابات الجبلية.png";
import point_2 from "../../../assets/images/ncw-map/البحر الاحمر.png";
import point_3 from "../../../assets/images/ncw-map/الخليج العربي.png";
import point_4 from "../../../assets/images/ncw-map/الرمال العربية.png";
import point_5 from "../../../assets/images/ncw-map/السهل الساحلي الغربي.png";
import point_6 from "../../../assets/images/ncw-map/السهل الساحلي للخليج العربي.png";
import point_7 from "../../../assets/images/ncw-map/المرتفعات الغربية.png";
import point_8 from "../../../assets/images/ncw-map/الهضاب العربية.png";
import point_9 from "../../../assets/images/ncw-map/صحراء المنطقة الشمالية.png";

import bg_point_1 from "../../../assets/images/ncw-map/الغابات الجبلية.jpg";
import bg_point_2 from "../../../assets/images/ncw-map/2121123.jpg";
import bg_point_3 from "../../../assets/images/ncw-map/bg3.jpg";
import bg_point_4 from "../../../assets/images/ncw-map/الكثبان الرملية.jpg";
import bg_point_5 from "../../../assets/images/ncw-map/السهل الساحلي الغربي1.jpg";
import bg_point_6 from "../../../assets/images/ncw-map/السهل الساحلي الشرقي.jpg";
import bg_point_7 from "../../../assets/images/ncw-map/المرتفعات الجبلية.jpg";
import bg_point_8 from "../../../assets/images/ncw-map/هضاب2.jpg";
import bg_point_9 from "../../../assets/images/ncw-map/الصحراء الشمالية.jpg";

export const pointImages = [
  point_1,
  point_2,
  point_3,
  point_4,
  point_5,
  point_6,
  point_7,
  point_8,
  point_9,
];

export const pointBgImages = [
  bg_point_1,
  bg_point_2,
  bg_point_3,
  bg_point_4,
  bg_point_5,
  bg_point_6,
  bg_point_7,
  bg_point_8,
  bg_point_9,
];

export const stats = {
  "2020": { sea: "6.48%", land: "18.10%" },
  "2025": { sea: "24%", land: "22%" },
  "2030": { sea: "30%", land: "30%" },
};

export const points = [
  {
    id: 1,
    name: "t(map2.points.0.name)",
    description: "t(map2.points.0.description)",
    type: "t(map2.points.0.type)",
    left: "37%",
    top: "65%",
    cardTitle: "t(map2.environmentalRegions.regions.mountainForests.title)",
    // cardSubtitle:
    //   "t(map2.environmentalRegions.regions.mountainForests.subtitle)",
    cardDescription:
      "t(map2.environmentalRegions.regions.mountainForests.description)",
    stats: { plants: "850", animals: "320" },
    area: "12,500 km²",
    elevation: "1,500-3,000m",
  },
  {
    id: 2,
    name: "t(map2.points.1.name)",
    description: "t(map2.points.1.description)",
    type: "t(map2.points.1.type)",
    left: "27%",
    top: "56%",
    cardTitle: "t(map2.environmentalRegions.regions.redSea.title)",
    // cardSubtitle: "t(map2.environmentalRegions.regions.redSea.subtitle)",
    cardDescription: "t(map2.environmentalRegions.regions.redSea.description)",
    stats: { plants: "450", animals: "1,200" },
    area: "438,000 km²",
    depth: "2,211m max",
  },
  {
    id: 3,
    name: "t(map2.points.2.name)",
    description: "t(map2.points.2.description)",
    type: "t(map2.points.2.type)",
    left: "64%",
    top: "25%",
    cardTitle: "t(map2.environmentalRegions.regions.arabianGulf.title)",
    // cardSubtitle: "t(map2.environmentalRegions.regions.arabianGulf.subtitle)",
    cardDescription:
      "t(map2.environmentalRegions.regions.arabianGulf.description)",
    stats: { plants: "380", animals: "890" },
    area: "251,000 km²",
    depth: "90m avg",
  },
  {
    id: 4,
    name: "t(map2.points.3.name)",
    description: "t(map2.points.3.description)",
    type: "t(map2.points.3.type)",
    left: "62%",
    top: "63%",
    cardTitle: "t(map2.environmentalRegions.regions.arabianSands.title)",
    cardSubtitle: "t(map2.environmentalRegions.regions.arabianSands.subtitle)",
    cardDescription:
      "t(map2.environmentalRegions.regions.arabianSands.description)",
    stats: { plants: "650", animals: "180" },
    area: "650,000 km²",
    temperature: "50°C max",
  },
  {
    id: 5,
    name: "t(map2.points.4.name)",
    description: "t(map2.points.4.description)",
    type: "t(map2.points.4.type)",
    left: "32%",
    top: "60%",
    cardTitle: "t(map2.environmentalRegions.regions.westernCoastalPlain.title)",
    // cardSubtitle:
    //   "t(map2.environmentalRegions.regions.westernCoastalPlain.subtitle)",
    cardDescription:
      "t(map2.environmentalRegions.regions.westernCoastalPlain.description)",
    stats: { plants: "520", animals: "450" },
    area: "8,500 km²",
    coastline: "1,100 km",
  },
  {
    id: 6,
    name: "t(map2.points.5.name)",
    description: "t(map2.points.5.description)",
    type: "t(map2.points.5.type)",
    left: "63%",
    top: "34%",
    cardTitle:
      "t(map2.environmentalRegions.regions.arabianGulfCoastalPlain.title)",
    // cardSubtitle:
    //   "t(map2.environmentalRegions.regions.arabianGulfCoastalPlain.subtitle)",
    cardDescription:
      "t(map2.environmentalRegions.regions.arabianGulfCoastalPlain.description)",
    stats: { plants: "480", animals: "680" },
    area: "12,000 km²",
    wetlands: "2,500 km²",
  },
  {
    id: 7,
    name: "t(map2.points.6.name)",
    description: "t(map2.points.6.description)",
    type: "t(map2.points.6.type)",
    left: "28%",
    top: "38%",
    cardTitle: "t(map2.environmentalRegions.regions.westernHighlands.title)",
    cardSubtitle:
      "t(map2.environmentalRegions.regions.westernHighlands.subtitle)",
    cardDescription:
      "t(map2.environmentalRegions.regions.westernHighlands.description)",
    stats: { plants: "720", animals: "280" },
    area: "15,000 km²",
    elevation: "2,000-3,000m",
  },
  {
    id: 8,
    name: "t(map2.points.7.name)",
    description: "t(map2.points.7.description)",
    type: "t(map2.points.7.type)",
    left: "42%",
    top: "46%",
    cardTitle: "t(map2.environmentalRegions.regions.arabianPlateaus.title)",
    // cardSubtitle:
    //   "t(map2.environmentalRegions.regions.arabianPlateaus.subtitle)",
    cardDescription:
      "t(map2.environmentalRegions.regions.arabianPlateaus.description)",
    stats: { plants: "580", animals: "220" },
    area: "25,000 km²",
    elevation: "800-1,200m",
  },
  {
    id: 9,
    name: "t(map2.points.8.name)",
    description: "t(map2.points.8.description)",
    type: "t(map2.points.8.type)",
    left: "30%",
    top: "12%",
    cardTitle:
      "t(map2.environmentalRegions.regions.northernRegionDesert.title)",
    // cardSubtitle:
    //   "t(map2.environmentalRegions.regions.northernRegionDesert.subtitle)",
    cardDescription:
      "t(map2.environmentalRegions.regions.northernRegionDesert.description)",
    stats: { plants: "420", animals: "150" },
    area: "180,000 km²",
    temperature: "45°C max",
  },
];
