import React from "react";
import { IconType } from "react-icons";

export interface IStatisticsItem {
  id: number;
  number: string;
  text: string;
  prefix: string;
  decimals: number;
}

export interface IServicesItem {
  id?: number;
  image: string;
  text: string;
  under_development: boolean;
  isInHome: boolean;
}

export interface IMarqueeItem {
  id: number;
  text: string;
}

export interface IPartnerItem {
  id: number;
  image: string;
}

export interface ISocialLink {
  id: number;
  Icon: IconType;
  to: string;
}

export interface IFooterLinks {
  id: number;
  title: string;
  links: IFooterLink[];
}

export interface IFooterLink {
  id: number;
  link: string;
  to: string;
}

export interface ISlideItem {
  id: number;
  image: string;
}

export interface INavLinkItem {
  id: number;
  link: string;
  to: string;
}

export interface IBreadcrumbItem {
  title: string;
  href: string;
}

export interface IContactItem {
  id: number;
  image: string;
  text: string;
  desc: string;
}

export interface IGetAllGuidesResponse {
  count: number;
  totalPages: number;
  next: string;
  prevURL: string;
  results: IGuideItem[];
}

export interface IGuideItem {
  id: number;
  img_path: string;
  name: string;
  name_en: string;
  // image: string;
  // text: string;
}

export interface IGetOneGuideListResponse {
  count: number;
  totalPages: number;
  next: string;
  prevURL: string;
  results: IOneGuideListItem[];
}

export interface IOneGuideListItem {
  id: number;
  name: string;
  name_en: string;
  brief: string;
  brief_en: string;
  description: string;
  description_en: string;
  media_path: string;
  classification_id: number;
  gis_code: null;
  references: null;
  reference_title: string;
  reference_url: string;
}

export interface IReferenceItem {
  id: number;
  title: string;
  website: string;
}

export interface IGalleryItem {
  id: number;
  item: string;
  isVideo: boolean;
  orientation: "landscape" | "portrait";
}

export interface IPhotoGalleryItem {
  id: number;
  images: string[];
  text: string;
  totalCount?: number;
}

export interface ISection {
  id: string;
  title: string;
  Section: () => React.ReactNode;
}

export interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string | null;
  department_id: number | null;
  remarks: string | null;
  province_id: number | null;
  minicipality_id: number | null;
  appusername: string;
  engcompany_id: number | null;
  issuer_id: number | null;
  token: string;
  position_id: number;
  is_active: number;
  reset_token: string | null;
  create_at: string | null;
  update_at: string | null;
  is_workflow_admin: boolean | null;
  image: string;
  owner_id: number | null;
  // issuers: any | null; // Replace 'any' with a more specific type if known
  // departments: any | null; // Replace 'any' with a more specific type if known
  engineering_companies: any | null; // Replace 'any' with a more specific type if known
  // municipalities: any | null; // Replace 'any' with a more specific type if known
  // provinces: any | null; // Replace 'any' with a more specific type if known
  // status: any | null; // Replace 'any' with a more specific type if known
  mobile: string | null;
  subMunicipilty_id: number | null;
  // imageGenerate: any | null; // Replace 'any' with a more specific type if known
  groups: Group[];
  positions: Position;
  // committees: any | null; // Replace 'any' with a more specific type if known
  is_super_admin: number;
  esriToken: string;
  // app_sub_count: any | null; // Replace 'any' with a more specific type if known
  deactivation_reason: string | null;
  is_readonly: boolean | null;
  is_readarchive: boolean | null;
  is_mahamy_admin: boolean;
  // subMunicipality: any | null; // Replace 'any' with a more specific type if known
  is_mahamy_director: boolean;
  user_type_id: number;
  user_type: UserType;
}

interface Group {
  id: number;
  name: string;
  department_id: number | null;
  is_mahamy: boolean;
  // departments: any | null; // Replace 'any' with a more specific type if known
  create_at: string;
  updateat: string | null;
  groups_permissions: GroupPermission[];
  // applications: any | null; // Replace 'any' with a more specific type if known
  // users: any | null; // Replace 'any' with a more specific type if known
  layers: Layer[];
}

interface GroupPermission {
  id: number;
  create: boolean | null;
  update: boolean | null;
  delete: boolean | null;
  get: number;
  group_id: number;
  module_id: number;
  app_id: number;
  apps_modules: AppsModule;
  // groups: any | null; // Replace 'any' with a more specific type if known
  applications: Application;
}

interface AppsModule {
  id: number;
  name: string;
  can_edit: number;
  settings: string | null;
  index: number | null;
  css_class: string | null;
  // applications: any | null; // Replace 'any' with a more specific type if known
}

export interface Application {
  id: number;
  name: string;
  icon: string | null;
  color: string | null;
  href: string | null;
  url: string;
  // translate: any | null; // Replace 'any' with a more specific type if known
  translate_ar_caption: string;
  translate_en_caption: string;
  max_request_no: number | null;
  // workflows: any | null; // Replace 'any' with a more specific type if known
  // apps_modules: any | null; // Replace 'any' with a more specific type if known
  // groups: any | null; // Replace 'any' with a more specific type if known
  // groups_permissions: any | null; // Replace 'any' with a more specific type if known
  // purpose: any | null; // Replace 'any' with a more specific type if known
  show_in_dashboard: boolean;
  can_search_inquiry: boolean;
}

interface Layer {
  id: number;
  name: string;
  arname: string;
  enname: string;
  is_editable: boolean;
  isToc: boolean;
  is_hidden: boolean;
  is_identify: boolean;
  outfields: Outfield[];
  dependencies: Dependency[];
}

interface Outfield {
  id: number;
  name: string;
  arname: string;
  is_editable: boolean;
  layer_id: number;
  is_public: boolean;
  is_search: boolean;
  is_hidden: boolean;
  enname: string;
  enalias: string;
  is_order: number;
  is_media: boolean;
}

interface Dependency {
  id: number;
  name: string;
}

interface Position {
  id: number;
  name: string;
  // users: any | null; // Replace 'any' with a more specific type if known
}

interface UserType {
  id: number;
  name: string;
  // special: any | null; // Replace 'any' with a more specific type if known
}
