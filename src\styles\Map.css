/* Map Component Styles */

.ncw-map {
  position: relative;
  color: #fff;
  overflow: hidden;
  background-color: var(--clr-secondary);
  /* margin-top: -5rem;
  padding-top: 5rem;
  padding-bottom: 5rem; */
  z-index: 5;
}

.ncw-map .bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(
    to bottom,
    rgba(37, 40, 41, 0.95) 0%,
    rgba(37, 40, 41, 0.8) 30%,
    rgba(37, 40, 41, 0.7) 70%,
    rgba(37, 40, 41, 0.9) 100%
  );
}

.ncw-map .map-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.ncw-map .map-bg.active {
  opacity: 1;
}

/* Map sidebar styles */
.map-sidebar {
  background-color: #000;
  border-radius: 16px;
  /* box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25); */
  padding: 24px !important;
  /* border: 1px solid rgba(255, 255, 255, 0.1); */
  height: 100%;
  /* margin-inline-start: auto; */
  width: 350px;
  border-radius: 20px !important;
}

/* Explorer button styling */
.map-sidebar .btn-outline-light {
  padding: 10px 15px;
  font-weight: 600;
  border-width: 2px;
  transition: all 0.3s ease;
  background-color: rgba(0, 0, 0, 0.2);
}

.map-sidebar .btn-outline-light:hover {
  background-color: #b45434;
  border-color: #b45434;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Tab styles */
.map-tabs {
  display: flex;
  /* border-radius: 12px; */
  overflow: hidden;
  /* background-color: rgba(0, 0, 0, 0.25); */
  margin-bottom: 20px;
  /* box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); */
}

.tab-item {
  flex: 1;
  padding: 14px 10px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  border-radius: 12px;
  margin: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #535353;
  height: 100px;
}

.tab-content .tab-pane {
  background-color: rgba(0, 0, 0, 0.2) !important;
  margin-bottom: 16px;
  padding-block: 0 !important;
  border-radius: 15px;
  display: flex;
  flex-direction: row-reverse;
}

/* .tab-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 25%;
  height: 50%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.2);
} */

.tab-item.active {
  background-color: #b45434;
  color: white;
  box-shadow: 0 0 10px rgba(180, 84, 52, 0.5);
  position: relative;
}

/* .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 3px;
  background-color: #fff;
  border-radius: 2px;
} */

/* Year tabs styles */
.year-tabs {
  display: flex;
  border-end-end-radius: 12px;
  border-start-end-radius: 12px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.25);
  /* margin-bottom: 20px; */
  /* box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); */
  flex-direction: column;
  width: 70px;
  flex-shrink: 0;
  position: relative;
  z-index: 100000;
  /* height: 300px; */
  /* border: 1px solid rgb(255 255 255 / 10%); */
}

.year-tab-item {
  flex: 1;
  padding: 14px 10px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* .year-tab-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 25%;
  height: 50%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.2);
} */

.year-tab-item:hover {
  background-color: rgba(0, 0, 0, 0.3);
  color: #fff;
}

.year-tab-item.active {
  background-color: #b45434;
  color: white;
  box-shadow: 0 0 10px rgba(180, 84, 52, 0.5);
}
/*
.year-tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 3px;
  background-color: #fff;
  border-radius: 2px;
} */

/* Year charts container */
.year-charts {
  /* background-color: rgba(0, 0, 0, 0.2); */
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); */
  display: flex;
  align-items: center;
  justify-self: center;
}

/* Chart container styles */
.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 70px;
  margin: auto;
  /* height: 50px; */
}

.chart-container > div {
  height: 100px !important;
  min-height: auto !important;
}

.apexcharts-canvas {
  height: 100% !important;
}

.chart-circle {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 4px solid;
  background-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.chart-container:first-child .chart-circle {
  border-color: #b45434;
  background: linear-gradient(
    145deg,
    rgba(180, 84, 52, 0.2),
    rgba(180, 84, 52, 0.4)
  );
  position: relative;
}

.chart-container:first-child .chart-circle::before {
  content: "";
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #b45434;
  box-shadow: 0 0 8px rgba(180, 84, 52, 0.8);
}

.chart-container:last-child .chart-circle {
  border-color: #7fcdff;
  background: linear-gradient(
    145deg,
    rgba(127, 205, 255, 0.2),
    rgba(127, 205, 255, 0.4)
  );
  position: relative;
}

.chart-container:last-child .chart-circle::before {
  content: "";
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #7fcdff;
  box-shadow: 0 0 8px rgba(127, 205, 255, 0.8);
}

.chart-percentage {
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.chart-label {
  margin-top: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

/* Protected areas list styles */
.protected-areas-list {
  margin-top: 25px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  /* max-height: 255px; */
  flex: 1;
  overflow: auto;
}

.protected-areas-list::-webkit-scrollbar {
  width: 8px;
}

.protected-areas-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.protected-areas-list::-webkit-scrollbar-thumb {
  background: #b45434;
  border-radius: 4px;
}

.protected-areas-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.list-title {
  font-weight: 700;
  font-size: 1.15rem;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 10px;
  margin-bottom: 15px;
  color: #fff;
}

.area-item {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  transition: all 0.3s ease;
  padding: 8px 10px;
  border-radius: 8px;
}

.area-item:hover {
  transform: translateX(-8px);
  background-color: rgba(255, 255, 255, 0.1);
}

.area-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-inline-end: 14px;
  /* box-shadow: 0 0 8px rgba(255, 255, 255, 0.4); */
  position: relative;
  flex-shrink: 0;
}

/* .area-indicator::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
} */

.area-name {
  font-size: 0.95rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
}

/* Button styles */
.btn-outline-light {
  border-color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  border-radius: 10px;
  padding: 12px 20px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.btn-outline-light:hover {
  background-color: #b45434;
  border-color: #b45434;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(180, 84, 52, 0.4);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .map-sidebar {
    margin-bottom: 20px;
  }
}

.ncw-map {
  position: relative;
}

@media (max-width: 768px) {
  .map-content,
  .protected-areas-list {
    display: none !important;
  }
  .map-sidebar {
    margin-inline: auto !important;
    height: auto !important;
  }
}

.ncw-map .map-content {
  position: absolute;
  z-index: -1;
  width: 100%;
  /* height: 600px; */
  border-radius: 10px;
  overflow: hidden;
  inset: 0;
}

/* .ncw-map .map-content::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(37, 40, 41, 0.7) 0%,
    rgba(37, 40, 41, 0.5) 20%,
    rgba(37, 40, 41, 0.3) 50%,
    rgba(37, 40, 41, 0.5) 80%,
    rgba(37, 40, 41, 0.7) 100%
  );
  pointer-events: none;
  z-index: 1;
  border-radius: 10px;
} */

.ncw-map .container-fluid {
  /* padding-block: 2rem; */
  width: 100%;
  max-width: 100%;
}

.esri-view {
  width: 100% !important;
  border-radius: 10px;
  /* overflow: auto; */
  height: 100% !important;
}

.map-sidebar {
  /* margin-right: auto; */
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.7) !important;
  height: 100%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  /* margin: 5rem auto 5rem 5rem; */
  margin: 5rem auto 5rem 2rem;
  height: 800px;
  display: flex;
  flex-direction: column;
}

.year-charts {
  flex: 1;
  justify-content: center;
}
canvas {
  max-width: 100% !important;
}
.year-tab-item {
  font-size: 12px;
}

/* Map Cards Styles */
.map-cards-container {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  margin-bottom: 1rem;
  z-index: 10;
}

.map-cards {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 0.5rem;
  border-radius: 10px;
}

@media (min-width: 768px) {
  .map-cards {
    position: absolute;
    bottom: 85px;
    left: 450px;
  }
}

.map-card {
  width: 120px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
}

.map-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  border-color: rgba(180, 84, 52, 0.5);
}

.map-card.active {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(180, 84, 52, 0.4);
  border: 2px solid #b45434;
}

.map-card-image {
  width: 100%;
  height: 80px;
  overflow: hidden;
  position: relative;
}

.map-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.map-card:hover .map-card-image img {
  transform: scale(1.05);
}

.map-card-title {
  padding: 0.5rem;
  margin: 0;
  text-align: center;
  font-size: 0.8rem;
  font-weight: 600;
  color: #fff;
}

@media (max-width: 768px) {
  .map-card {
    width: 100px;
  }

  .map-card-image {
    height: 70px;
  }

  .map-card-title {
    font-size: 0.7rem;
    padding: 0.3rem;
  }
}
