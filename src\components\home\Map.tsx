import ncw_map_bg1 from "../../assets/images/ncw-map/bg1.jpg";
import ncw_map_bg2 from "../../assets/images/ncw-map/خلفية النظم البحرية.jpg";
import ncw_map_bg3 from "../../assets/images/ncw-map/bg3.webp";

import light_image from "../../assets/images/light.jpeg";
import dark_image from "../../assets/images/dark.jpg";
import VectorTileLayer from "@arcgis/core/layers/VectorTileLayer";
import Basemap from "@arcgis/core/Basemap";
// Note: We're keeping the imports for the background images, but using SVG for the map cards
import "../../styles/Map.css";
import { useEffect, useState, useContext } from "react";
import type { ApexOptions } from "apexcharts";
import ReactApexChart from "react-apexcharts";
import { MapContext } from "../mapFolder/MapContext";
import { getFeaturesByUniqueValue } from "../mapFolder/MapUtils";
import { MapComponent } from "../mapFolder/MapComponent";
import { Link } from "react-router";
import { urls } from "../../utils/urls";
import { useTranslation } from "react-i18next";
type ChartType = NonNullable<ApexOptions["chart"]>["type"];

export default function Map() {
  // const { map, mapView, mapInfo } = useContext(MapContext);
  const map_context = useContext(MapContext);
  const { t } = useTranslation();
  const [isClient, setIsClient] = useState(false);
  const [charts, setCharts] = useState<ApexCharts[]>([]);
  const [selectedBoundaryStatus, setSelectedBoundaryStatus] = useState("PA");
  const [selectedEcosystemTab, setSelectedEcosystemTab] = useState(
    "PROTECTED_AREA_BOUNDARY"
  );
  const vectorTileLayer = new VectorTileLayer({
    url: "https://www.arcgis.com/sharing/rest/content/items/5e9b3685f4c24d8781073dd928ebda50/resources/styles/root.json",
    // Replace with your custom vector style with no labels
  });

  const customBasemap = new Basemap({
    baseLayers: [vectorTileLayer],
    title: "Dark Vector No Labels",
    id: "dark-no-labels",
  });
  const [legendList, setLegendList] = useState([]);
  const [isDarkMode, setIsDarkMode] = useState(false);
  useEffect(() => {
    if (
      !map_context?.map ||
      !map_context?.mapView ||
      !map_context?.mapInfo ||
      !selectedEcosystemTab
    )
      return;
    const layer = map_context.map.layers.items.find(
      (l: any) => l.id === selectedEcosystemTab
    );
    const otherLayers = map_context.map.layers.items.filter(
      (l: any) =>
        l.id != selectedEcosystemTab && !l.isBaseMap && l.id != "baseMap"
    );
    if (!layer) return;

    map_context.mapView
      .whenLayerView(layer)
      .then((layerView: __esri.LayerView) => {
        // if a feature is already highlighted, then remove the highlight
        layer.__layerView = layerView;
      });

    layer.when().then(() => {
      layer.visible = true;
      if (selectedEcosystemTab == "PROTECTED_AREA_BOUNDARY") {
        updateLegendListBasedOnSelectedStatus(layer);
      } else {
        let uniqueValueInfos = layer.renderer.uniqueValueInfos.map(
          (item: any) => {
            return {
              label: item.label,
              color: item.symbol.color,
              value: item.value,
            };
          }
        );
        setLegendList(uniqueValueInfos);
      }
    });

    otherLayers.forEach((layer: any) => {
      layer.visible = false;
    });
  }, [
    map_context?.map,
    map_context?.mapView,
    map_context?.mapInfo,
    selectedEcosystemTab,
  ]);

  useEffect(() => {
    if (map_context?.map && map_context?.mapInfo && map_context?.mapView) {
      if (selectedEcosystemTab === "PROTECTED_AREA_BOUNDARY") {
        let layer = map_context.map.layers.items.find(
          (item: any) => item.id === selectedEcosystemTab
        );

        let types = selectedBoundaryStatus.split(",").map((d) => {
          return `EN_PROTECTED_AREA_STATUS = '${d}'`;
        });
        layer.definitionExpression = types.join(" or ");
        const graphicLayer = map_context.map.layers.items.find(
          (layer: any) => layer.id == "highlightGraphicLayer"
        );
        graphicLayer.removeAll();
        updateLegendListBasedOnSelectedStatus(layer);
      }
    }
  }, [selectedBoundaryStatus]);

  useEffect(() => {
    if (map_context?.map) {
      if (isDarkMode) {
        map_context.map.basemap = customBasemap;
      } else {
        map_context.map.basemap = "satellite";
      }
    }
  }, [isDarkMode]);

  const updateLegendListBasedOnSelectedStatus = (layer: any) => {
    let uniqueValueInfos = layer.renderer.uniqueValueInfos.reduce(
      (acc: any, item: any) => {
        //let values = item.value.split(",");

        acc.push({
          label: item.label,
          color: item.symbol.color,
          value: item.value,
        });

        return acc;
      },
      []
    );
    setLegendList(uniqueValueInfos);
  };

  // This effect would handle dark mode changes if we wanted to actually change the map
  // We're just keeping the design elements without changing the actual map

  useEffect(() => {
    setIsClient(true);

    const initializeCharts = async () => {
      try {
        const ApexCharts = (await import("apexcharts")).default;

        // Create chart instances
        const chartConfigs = [
          {
            element: ".chart-2030",
            options: state_2030_lang.options,
            series: state_2030_sea.series,
          },
          {
            element: ".chart-2024-sea",
            options: state_2024_sea.options,
            series: state_2024_sea.series,
          },
          {
            element: ".chart-2024-land",
            options: state_2024_land.options,
            series: state_2024_land.series,
          },
          {
            element: ".chart-2017-land",
            options: state_2017_land.options,
            series: state_2017_land.series,
          },
          {
            element: ".chart-2017-sea",
            options: state_2017_sea.options,
            series: state_2017_sea.series,
          },
          {
            element: ".chart-2025-sea",
            options: state_2025_sea.options,
            series: state_2025_sea.series,
          },
          {
            element: ".chart-2025-land",
            options: state_2025_land.options,
            series: state_2025_land.series,
          },
        ];

        const newCharts = chartConfigs
          .map((config) => {
            const el = document.querySelector(config.element);
            if (el) {
              const chart = new ApexCharts(el, {
                ...config.options,
                series: config.series,
              });
              chart.render();
              return chart;
            }
            return null;
          })
          .filter(Boolean);

        setCharts(newCharts as ApexCharts[]);
      } catch (error) {
        console.error("Error initializing charts:", error);
      }
    };

    if (isClient) {
      initializeCharts();
    }

    return () => {
      charts.forEach((chart) => {
        try {
          chart.destroy();
        } catch (e) {
          console.error("Error destroying chart:", e);
        }
      });
    };
  }, [isClient]);

  const [state_2030_lang] = useState({
    series: [30],
    options: {
      chart: {
        height: 350,
        type: "radialBar" as ChartType,
        offsetY: -20,
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          hollow: {
            size: "70%",
          },
          track: {
            background: "#fff",
            strokeWidth: "97%",
            opacity: 0.1,
            margin: 0,
          },
          barStrokeWidth: 0,
          dataLabels: {
            show: true,
            name: {
              show: false,
            },
            value: {
              show: true,
              fontSize: "12px",
              fontWeight: 600,
              color: "#fff",
              offsetY: -10,
              formatter: function (val: number) {
                return val + "%";
              },
            },
          },
        },
      },
      fill: {
        colors: ["#b45434"], // Add this to change the color to red
      },
      labels: [""],
    },
  });
  const [state_2030_sea] = useState({
    series: [30],
    options: {
      chart: {
        height: 350,
        type: "radialBar" as ChartType,
        offsetY: -20,
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          hollow: {
            size: "70%",
          },
          track: {
            background: "#fff",
            strokeWidth: "97%",
            opacity: 0.1,
            margin: 0,
          },
          barStrokeWidth: 0,
          dataLabels: {
            show: true,
            name: {
              show: false,
            },
            value: {
              show: true,
              fontSize: "12px",
              fontWeight: 600,
              color: "#fff",
              offsetY: -10,
              formatter: function (val: number) {
                return val + "%";
              },
            },
          },
        },
      },
      fill: {
        colors: ["#7fcdff"], // Add this to change the color to blue
      },
      labels: [""],
    },
  });
  const [state_2024_sea] = useState({
    series: [24],
    options: {
      chart: {
        height: 350,
        type: "radialBar" as ChartType,
        offsetY: -20,
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          hollow: {
            size: "70%",
          },
          track: {
            background: "#fff",
            strokeWidth: "97%",
            opacity: 1,
            margin: 0,
          },
          barStrokeWidth: 0,
          dataLabels: {
            show: true,
            name: {
              show: false,
            },
            value: {
              show: true,
              fontSize: "12px",
              fontWeight: 600,
              color: "#fff",
              offsetY: -10,
              formatter: function (val: number) {
                return val + "%";
              },
            },
          },
        },
      },
      fill: {
        colors: ["#7fcdff"], // Add this to change the color to blue
      },
      labels: [""],
    },
  });
  const [state_2024_land] = useState({
    series: [22],
    options: {
      chart: {
        height: 350,
        type: "radialBar" as ChartType,
        offsetY: -20,
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          hollow: {
            size: "70%",
          },
          track: {
            background: "#fff",
            strokeWidth: "97%",
            opacity: 1,
            margin: 0,
          },
          barStrokeWidth: 0,
          dataLabels: {
            show: true,
            name: {
              show: false,
            },
            value: {
              show: true,
              fontSize: "12px",
              fontWeight: 600,
              color: "#fff",
              offsetY: -10,
              formatter: function (val: number) {
                return val + "%";
              },
            },
          },
        },
      },
      fill: {
        colors: ["#b45434"], // Add this to change the color to red
      },
      labels: [""],
    },
  });
  const [state_2017_land] = useState({
    series: [30],
    options: {
      chart: {
        height: 350,
        type: "radialBar" as ChartType,
        offsetY: -20,
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          hollow: {
            size: "70%",
          },
          track: {
            background: "#fff",
            strokeWidth: "97%",
            opacity: 1,
            margin: 0,
          },
          barStrokeWidth: 0,
          dataLabels: {
            show: true,
            name: {
              show: false,
            },
            value: {
              show: true,
              fontSize: "12px",
              fontWeight: 600,
              color: "#fff",
              offsetY: -10,
              formatter: function (val: number) {
                return val + "%";
              },
            },
          },
        },
      },
      fill: {
        colors: ["#b45434"], // Add this to change the color to red
      },
      labels: [""],
    },
  });
  const [state_2017_sea] = useState({
    series: [30],
    options: {
      chart: {
        height: 350,
        type: "radialBar" as ChartType,
        offsetY: -20,
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          hollow: {
            size: "70%",
          },
          track: {
            background: "#fff",
            strokeWidth: "97%",
            opacity: 1,
            margin: 0,
          },
          barStrokeWidth: 0,
          dataLabels: {
            show: true,
            name: {
              show: false,
            },
            value: {
              show: true,
              fontSize: "12px",
              fontWeight: 600,
              color: "#fff",
              offsetY: -10,
              formatter: function (val: number) {
                return val + "%";
              },
            },
          },
        },
      },
      fill: {
        colors: ["#7fcdff"], // Add this to change the color to blue
      },
      labels: [""],
    },
  });
  const [state_2025_sea] = useState({
    series: [6.48],
    options: {
      chart: {
        height: 350,
        type: "radialBar" as ChartType,
        offsetY: -20,
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          hollow: {
            size: "70%",
          },
          track: {
            background: "#fff",
            strokeWidth: "97%",
            opacity: 1,
            margin: 0,
          },
          barStrokeWidth: 0,
          dataLabels: {
            show: true,
            name: {
              show: false,
            },
            value: {
              show: true,
              fontSize: "12px",
              fontWeight: 600,
              color: "#fff",
              offsetY: -10,
              formatter: function (val: number) {
                return val + "%";
              },
            },
          },
        },
      },
      fill: {
        colors: ["#7fcdff"], // Add this to change the color to blue
      },
      labels: [""],
    },
  });
  const [state_2025_land] = useState({
    series: [18.1],
    options: {
      chart: {
        height: 350,
        type: "radialBar" as ChartType,
        offsetY: -20,
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          hollow: {
            size: "70%",
          },
          track: {
            background: "#fff",
            strokeWidth: "97%",
            opacity: 1,
            margin: 0,
          },
          barStrokeWidth: 0,
          dataLabels: {
            show: true,
            name: {
              show: false,
            },
            value: {
              show: true,
              fontSize: "12px",
              fontWeight: 600,
              color: "#fff",
              offsetY: -10,
              formatter: function (val: number) {
                return val + "%";
              },
            },
          },
        },
      },
      fill: {
        colors: ["#b45434"], // Add this to change the color to red
      },
      labels: [""],
    },
  });

  const removeHighlight = (item: any) => {
    console.log(item);
    const targetLayer = map_context?.map.layers.items.find(
      (layer: any) => layer.id == selectedEcosystemTab
    );

    if (targetLayer.__layerView._highlightIds.size > 0)
      targetLayer.__layerView._highlightIds.clear();
  };

  const handleLegendItemClick = async (item: any) => {
    const targetLayer = map_context?.map.layers.items.find(
      (layer: any) => layer.id == selectedEcosystemTab
    );
    const values = item.value.split(",");
    let features;
    if (values.length > 1) {
      features = await getFeaturesByUniqueValue(
        targetLayer,
        [targetLayer.renderer.field, targetLayer.renderer.field2],
        values
      );
    } else {
      features = await getFeaturesByUniqueValue(
        targetLayer,
        targetLayer.renderer.field,
        item.value
      );
    }

    (map_context?.mapView as any).highlightOptions = {
      color: [180, 83, 51],
      haloOpacity: 1,
      fillOpacity: 1,
      haloColor: [0, 0, 0, 1],
    };

    targetLayer.__layerView._highlightIds.clear();
    targetLayer.__layerView.highlight(features);
  };

  return (
    <>
      <section className="ncw-map" id="ncwMap">
        <div className="bg">
          <img
            src={ncw_map_bg1}
            alt=""
            className={`map-bg ${
              selectedEcosystemTab === "PROTECTED_AREA_BOUNDARY" && "active"
            }`}
            id="bg-img-1"
          />
          <img
            src={ncw_map_bg2}
            alt=""
            className={`map-bg ${
              selectedEcosystemTab === "ECO_SYSTEM_MARINE" && "active"
            }`}
            id="bg-img-2"
          />
          <img
            src={ncw_map_bg3}
            alt=""
            className={`map-bg ${
              selectedEcosystemTab === "ECO_SYSTEM_TERRESTIAL" && "active"
            }`}
            id="bg-img-3"
          />
        </div>

        <div className="container-fluid">
          <div className="row g-3 align-items-center">
            <div className="col-12 col-xl-5">
              <div className="section-header flex-column align-items-start text-white">
                <h5
                  className="section-title"
                  // data-aos="fade-up"
                >
                  {t("interactiveMap.title")}{" "}
                  <span className="colored">
                    {t("interactiveMap.titleHighlight")}
                  </span>
                </h5>
                {/* <p className="text-white">
                توفر الخريطة التفاعلية بيانات دقيقة وشاملة عن الحياة الفطرية
              </p> */}
              </div>
            </div>
          </div>

          <div
            style={{
              display: "flex",
              gap: "15px",
              flexWrap: "wrap",
              position: "relative",
              zIndex: 2,
              width: "100%",
            }}
          >
            {/* Map content will be displayed here */}
            <div className="map-content">
              <MapComponent />
            </div>

            {/* Left sidebar with tabs and charts */}
            <div className="map-sidebar bg-dark p-3 rounded">
              {/* Ecosystem Tabs */}
              <div className="map-tabs d-flex mb-4">
                <div
                  className={`tab-item p-2 text-center ${
                    selectedEcosystemTab === "PROTECTED_AREA_BOUNDARY"
                      ? "active"
                      : ""
                  }`}
                  onClick={() => {
                    setSelectedEcosystemTab("PROTECTED_AREA_BOUNDARY");
                  }}
                >
                  {t("interactiveMap.tabs.protectedAreas")}
                </div>
                <div
                  className={`tab-item p-2 text-center ${
                    selectedEcosystemTab === "ECO_SYSTEM_MARINE" ? "active" : ""
                  }`}
                  onClick={() => {
                    setSelectedEcosystemTab("ECO_SYSTEM_MARINE");
                  }}
                >
                  {t("interactiveMap.tabs.marineEcosystems")}
                </div>
                <div
                  className={`tab-item p-2 text-center ${
                    selectedEcosystemTab === "ECO_SYSTEM_TERRESTIAL"
                      ? "active"
                      : ""
                  }`}
                  onClick={() => {
                    setSelectedEcosystemTab("ECO_SYSTEM_TERRESTIAL");
                  }}
                >
                  {t("interactiveMap.tabs.terrestrialEcosystems")}
                </div>
              </div>

              {/* Tab content */}
              <div className="tab-content mt-4">
                {/* Protected Areas */}
                {selectedEcosystemTab === "PROTECTED_AREA_BOUNDARY" && (
                  <div className="tab-pane active">
                    {/*  */}
                    {/* Year tabs */}
                    <div className="year-tabs">
                      <div
                        className={`year-tab-item ${
                          selectedBoundaryStatus === "PA" ? "active" : ""
                        }`}
                        onClick={() => setSelectedBoundaryStatus("PA")}
                      >
                        {t("interactiveMap.yearTabs.current")}
                      </div>
                      <div
                        className={`year-tab-item ${
                          selectedBoundaryStatus === "PA,PPA_25" ? "active" : ""
                        }`}
                        onClick={() => setSelectedBoundaryStatus("PA,PPA_25")}
                      >
                        {t("interactiveMap.yearTabs.proposed2025")}
                      </div>
                      <div
                        className={`year-tab-item ${
                          selectedBoundaryStatus === "PA,PPA_25,PPA_30"
                            ? "active"
                            : ""
                        }`}
                        onClick={() =>
                          setSelectedBoundaryStatus("PA,PPA_25,PPA_30")
                        }
                      >
                        {t("interactiveMap.yearTabs.proposed2030")}
                      </div>
                    </div>

                    {/* Year charts */}
                    <div className="year-charts">
                      {selectedBoundaryStatus === "PA" && (
                        <div
                          className="d-flex justify-content-around mt-2"
                          style={{ flexDirection: "column" }}
                        >
                          <div className="chart-container">
                            <ReactApexChart
                              options={state_2025_land.options}
                              series={state_2025_land.series}
                              type="radialBar"
                              height={150}
                            />
                            <span
                              style={{
                                fontSize: "18px",
                                fontWeight: "bold",
                                marginTop: "-20px",
                                marginBottom: "20px",
                              }}
                            >
                              {t("interactiveMap.charts.terrestrial")}
                            </span>
                          </div>
                          <div className="chart-container">
                            <ReactApexChart
                              options={state_2025_sea.options}
                              series={state_2025_sea.series}
                              type="radialBar"
                              height={150}
                            />
                            <span
                              style={{
                                fontSize: "18px",
                                fontWeight: "bold",
                                marginTop: "-20px",
                                marginBottom: "20px",
                              }}
                            >
                              {t("interactiveMap.charts.marine")}
                            </span>
                          </div>
                        </div>
                      )}

                      {selectedBoundaryStatus === "PA,PPA_25" && (
                        <div
                          className="d-flex justify-content-around mt-2"
                          style={{ flexDirection: "column" }}
                        >
                          <div className="chart-container">
                            <ReactApexChart
                              options={state_2024_land.options}
                              series={state_2024_land.series}
                              type="radialBar"
                              height={150}
                            />
                            <span
                              style={{
                                fontSize: "18px",
                                fontWeight: "bold",
                                marginTop: "-20px",
                                marginBottom: "20px",
                              }}
                            >
                              {t("interactiveMap.charts.terrestrial")}
                            </span>
                          </div>
                          <div className="chart-container">
                            <ReactApexChart
                              options={state_2024_sea.options}
                              series={state_2024_sea.series}
                              type="radialBar"
                              height={150}
                            />
                            <span
                              style={{
                                fontSize: "18px",
                                fontWeight: "bold",
                                marginTop: "-20px",
                                marginBottom: "20px",
                              }}
                            >
                              {t("interactiveMap.charts.marine")}
                            </span>
                          </div>
                        </div>
                      )}

                      {selectedBoundaryStatus === "PA,PPA_25,PPA_30" && (
                        <div
                          className="d-flex justify-content-around mt-2"
                          style={{ flexDirection: "column" }}
                        >
                          <div className="chart-container">
                            <ReactApexChart
                              options={state_2017_land.options}
                              series={state_2017_land.series}
                              type="radialBar"
                              height={150}
                            />
                            <span
                              style={{
                                fontSize: "18px",
                                fontWeight: "bold",
                                marginTop: "-20px",
                                marginBottom: "20px",
                              }}
                            >
                              {t("interactiveMap.charts.terrestrial")}
                            </span>
                          </div>
                          <div className="chart-container">
                            <ReactApexChart
                              options={state_2017_sea.options}
                              series={state_2017_sea.series}
                              type="radialBar"
                              height={150}
                            />
                            <span
                              style={{
                                fontSize: "18px",
                                fontWeight: "bold",
                                marginTop: "-20px",
                                marginBottom: "20px",
                              }}
                            >
                              {t("interactiveMap.charts.marine")}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                    {/*  */}
                  </div>
                )}

                {/* Marine Ecosystems */}
                {selectedEcosystemTab === "ECO_SYSTEM_MARINE" && (
                  <div
                    className="tab-pane active"
                    style={{ flexDirection: "row" }}
                  >
                    {/*  */}
                    {/* Year tabs */}
                    {/* <div className="year-tabs">
                    <div
                      className={`year-tab-item active`}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      2030
                    </div>
                  </div> */}

                    {/* Year charts */}
                    {/* <div className="year-charts">
                    <div
                      className="d-flex justify-content-around items-center"
                      style={{ fontSize: "30px" }}
                    >
                      16
                    </div>
                  </div> */}

                    <div style={{ padding: "10px" }}>
                      {t("interactiveMap.ecosystemCount.marine")}{" "}
                      <span
                        style={{
                          fontSize: "30px",
                          color: "#b45434",
                          marginInline: "10px",
                        }}
                      >
                        16
                      </span>
                    </div>
                    {/*  */}
                  </div>
                )}

                {/* Terrestrial Ecosystems */}
                {selectedEcosystemTab === "ECO_SYSTEM_TERRESTIAL" && (
                  <div
                    className="tab-pane active"
                    style={{ flexDirection: "row" }}
                  >
                    {/*  */}
                    {/* Year tabs */}
                    {/* <div className="year-tabs">
                    <div
                      className={`year-tab-item active`}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      2030
                    </div>
                  </div> */}

                    {/* Year charts */}
                    {/* <div className="year-charts">
                    <div
                      className="d-flex justify-content-around items-center"
                      style={{ fontSize: "30px" }}
                    >
                      49
                    </div>
                  </div> */}
                    {/*  */}

                    <div style={{ padding: "10px" }}>
                      {t("interactiveMap.ecosystemCount.terrestrial")}{" "}
                      <span
                        style={{
                          fontSize: "30px",
                          color: "#b45434",
                          marginInline: "10px",
                        }}
                      >
                        49
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Protected areas list */}
              <div className="protected-areas-list">
                <ul className="list-unstyled">
                  {legendList.length > 0 &&
                    legendList.map((item: any) => {
                      return (
                        <li
                          className="area-item d-flex align-items-center mb-2"
                          // onClick={() => {
                          //   handleLegendItemClick(item);
                          // }}
                          onMouseEnter={() => handleLegendItemClick(item)}
                          onMouseLeave={() => removeHighlight(item)}
                        >
                          <span
                            className="area-indicator"
                            style={{
                              backgroundColor: `rgba(${item.color.r}, ${item.color.g}, ${item.color.b}, ${item.color.a})`,
                            }}
                          ></span>
                          <span className="area-name">{item.label}</span>
                        </li>
                      );
                    })}
                </ul>
              </div>

              {/* Wildlife Rasid   Explorer button */}
              <div className="text-center mt-4">
                <Link to={urls.wildlifeExplorer} target="_blank">
                  <button
                    className="btn btn-outline-light w-100"
                    style={{ borderRadius: "30px" }}
                  >
                    {t("interactiveMap.geoExplorer")}
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="map-cards-section">
        <div className="">
          <div className="map-cards-container">
            <div className="map-cards">
              <div
                className={`map-card ${!isDarkMode ? "active" : ""}`}
                onClick={() => setIsDarkMode(false)}
              >
                <div className="map-card-image">
                  <img src={light_image} alt="" />
                </div>
                {/* <h4 className="map-card-title">
                  {t("interactiveMap.mapModes.light")}
                </h4> */}
              </div>

              <div
                className={`map-card ${isDarkMode ? "active" : ""}`}
                onClick={() => setIsDarkMode(true)}
              >
                <div className="map-card-image">
                  <img src={dark_image} alt="" />
                </div>
                {/* <h4 className="map-card-title">
                  {t("interactiveMap.mapModes.dark")}
                </h4> */}
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
