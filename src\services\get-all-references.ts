import endpoint from "../api/endpoint";
import { referencesAtom } from "../atoms/references-atom";

export const getAllReferences = async () => {
  try {
    /***
     * start loading
     */
    referencesAtom.change("isLoading", true);

    /****
     * get all references
     */
    const { data } = await endpoint.get("api/Reference/GetAll");

    /***
     * set references to atom
     */
    referencesAtom.change("references", data.results);
  } catch (error) {
    /****
     * set error
     */
    referencesAtom.change("error", (error as Error).message);
  } finally {
    /***
     * end loading
     */
    referencesAtom.change("isLoading", false);
  }
};
