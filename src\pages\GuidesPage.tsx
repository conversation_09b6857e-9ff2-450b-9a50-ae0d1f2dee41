import GuidesTabs from "../components/guides/GuidesTabs";
import { useTranslation } from "react-i18next";
import PageLayout from "../components/layouts/PageLayout";

export default function GuidesPage() {
  const { t } = useTranslation();

  return (
    <PageLayout
      breadcrumbTitle={t("guidesPage.breadcrumb")}
      backgroundAlt={t("guidesPage.backgroundAlt")}
    >
      <div className="container-fluid">
        <GuidesTabs />
      </div>
    </PageLayout>
  );
}
