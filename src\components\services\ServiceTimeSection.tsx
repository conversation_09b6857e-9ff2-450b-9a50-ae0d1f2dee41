import SectionTitle from "../common/SectionTitle";

interface ServiceTimeSectionProps {
  title: string;
  coloredPart?: string;
  description: string;
  className?: string;
}

/**
 * A reusable component for displaying service time information in service pages
 */
const ServiceTimeSection: React.FC<ServiceTimeSectionProps> = ({
  title,
  coloredPart,
  description,
  className = "",
}) => {
  return (
    <div className={className}>
      <SectionTitle
        title={title}
        coloredPart={coloredPart}
        description={description}
      />
    </div>
  );
};

export default ServiceTimeSection;
