import { useTranslation } from "react-i18next";
import ServiceDetailsLayout from "../components/layouts/ServiceDetailsLayout";
import SectionTitle from "../components/common/SectionTitle";
import BeneficiariesList from "../components/services/BeneficiariesList";
import ServiceTimeSection from "../components/services/ServiceTimeSection";

export default function RequestDataDetailsPage() {
  const { t } = useTranslation();

  const descriptionWithServices = (
    <>
      {t("requestDataDetailsPage.description")}{" "}
      <ul className="section-list">
        {(
          t("requestDataDetailsPage.services", {
            returnObjects: true,
          }) as string[]
        ).map((service: string, index: number) => (
          <li key={index} className="section-description">
            {service}
          </li>
        ))}
      </ul>
    </>
  );

  return (
    <ServiceDetailsLayout
      breadcrumbTitle={t("requestDataDetailsPage.breadcrumb")}
      downloadGuideText={t("requestDataDetailsPage.downloadGuide")}
      downloadCardContent={
        <a href="#" className="btn btn-outline-secondary">
          {t("requestDataDetailsPage.startService")}
        </a>
      }
    >
      <SectionTitle
        title={t("requestDataDetailsPage.title")}
        description={descriptionWithServices}
      />

      <div>
        <SectionTitle
          title={t("requestDataDetailsPage.procedures.title").split(" ")[0]}
          coloredPart={
            t("requestDataDetailsPage.procedures.title").split(" ")[1]
          }
        />

        <ul
          className="section-list"
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(400px, 1fr))",
            width: "100%",
          }}
        >
          {(
            t("requestDataDetailsPage.procedures.steps", {
              returnObjects: true,
            }) as string[]
          ).map((step: string, index: number) => (
            <li key={index} className="section-description">
              {step}
            </li>
          ))}
        </ul>
      </div>

      <BeneficiariesList
        title={t("requestDataDetailsPage.beneficiaries.title").split(" ")[0]}
        coloredPart={
          t("requestDataDetailsPage.beneficiaries.title").split(" ")[1]
        }
        beneficiaries={
          t("requestDataDetailsPage.beneficiaries.list", {
            returnObjects: true,
          }) as string[]
        }
      />

      {/* <div>
        <SectionTitle
          title={t("requestDataDetailsPage.terms.title").split(" ")[0]}
          coloredPart={t("requestDataDetailsPage.terms.title").split(" ")[1]}
          description={t("requestDataDetailsPage.terms.description")}
        />
      </div> */}

      <ServiceTimeSection
        title={t("requestDataDetailsPage.serviceTime.title").split(" ")[0]}
        coloredPart={t("requestDataDetailsPage.serviceTime.title")
          .split(" ")
          .slice(1)
          .join(" ")}
        description={t("requestDataDetailsPage.serviceTime.description")}
      />
    </ServiceDetailsLayout>
  );
}
