export const en_locales = {
  footer: {
    mobileApp: {
      title: "Wildlife Rasid  App",
      description:
        "An application that runs on smart devices to Rasid  and collect biodiversity data and important environmental sites, and to record the field routes of researchers and interested parties",
    },
    importantLinks: {
      title: "Important Links",
      links: [
        "National Center for Wildlife Development",
        "National Unified Platform",
        "Website Terms of Use",
        "Privacy Policy",
      ],
    },
    monitorPlatform: {
      title: "Wildlife Rasid  Platform",
      links: ["Vision", "Goals"],
    },
    contactUs: {
      title: "Contact Us",
      links: ["FAQ", "Contact Us"],
    },
    socialMedia: {
      tooltips: {
        youtube: "YouTube",
        linkedin: "LinkedIn",
        twitter: "Twitter",
        facebook: "Facebook",
      },
    },
    copyright:
      "All rights reserved to the National Center for Wildlife Development",
  },
  navbar: {
    home: "Home",
    about: "About the Department",
    services: "Portal Services",
    guides: "Guides and References",
    login: "Login",
    logout: "Logout",
  },
  navigation: {
    home: "Home",
    map: "Map",
    services: "Services",
    statistics: "Statistics",
  },
  guidesTabs: {
    guides: "Guides",
    references: "References",
  },
  guidesPage: {
    breadcrumb: "Guides and References",
    backgroundAlt: "Map Background",
  },
  homePage: "Home Page",
  heroSection: {
    title: "Explore wildlife in the Wildlife Rasid  Platform",
    title2: "We plan and develop",
    description:
      "Design and build a central geospatial database - Includes all biodiversity data in the Kingdom and associated use layers to serve as the primary reference for biodiversity data in the Kingdom of Saudi Arabia",
    learnMore: "Learn More",
    imageAlt: "Wildlife Image",
    backgroundAlt: "Wildlife Background",
  },
  monitorSection: {
    title: "We Rasid  and analyze",
    description:
      "Wildlife Rasid  - Monitoring and collecting biodiversity data to facilitate, automate and synchronize field work and activate community participation to raise awareness of the importance of preserving wildlife",
  },
  exploreSection: {
    title: "We produce and map",
    description:
      "Wildlife Geo Explorer - Providing interactive maps to facilitate data browsing and access to accurate and important information as quickly as possible and in an advanced interactive manner",
  },
  protectSection: {
    title: "We protect and develop",
    description:
      "Decision-making support - Displaying environmental data and indicators in interactive dashboards supports decision-makers and stakeholders and contributes to achieving the center's vision of a prosperous and sustainable wildlife",
  },
  interactiveMap: {
    title: "Interactive Map",
    titleHighlight: "displaying wildlife data",
    protectedAreas: "Protected areas with the same old values",
    ecosystems: "16 marine ecosystems + 49 terrestrial ecosystems",
    tabs: {
      protectedAreas: "Protected Areas",
      marineEcosystems: "Marine Ecosystems",
      terrestrialEcosystems: "Terrestrial Ecosystems",
    },
    yearTabs: {
      current: "Current",
      proposed2025: "Proposed 2025",
      proposed2030: "Proposed 2030",
    },
    charts: {
      terrestrial: "Terrestrial",
      marine: "Marine",
    },
    ecosystemCount: {
      marine: "Marine ecosystem",
      terrestrial: "Terrestrial ecosystem",
    },
    geoExplorer: "Wildlife Rasid   Explorer",
    mapModes: {
      light: "Light Mode",
      dark: "Dark Mode",
    },
  },
  mainServices: {
    title: "Main Services",
    sectionTitle: "Wildlife Rasid  Platform",
    sectionTitleHighlight: "Services",
    sectionDescription:
      "The Wildlife Rasid  platform provides an integrated set of digital services designed to support wildlife protection",
    exploreMore: "Explore More",
    geoExplorer: {
      title: "Geo Explorer",
      description:
        "A service that provides a number of geospatial tools and functions via interactive maps",
    },
    wildlifeMonitor: {
      title: "Al-Raed Alfitry",
      description:
        "An application that runs on smart devices to Rasid  and collect data on biodiversity and important environmental sites, and to record the field routes of researchers and interested parties",
    },
    decisionSupport: {
      title: "Decision Support",
      description:
        "A service that displays environmental data and indicators in interactive dashboards",
    },
    fieldStudies: {
      title: "Field Studies",
      description:
        "A service that provides a number of geospatial tools and functions for field study trips",
    },
  },
  performanceIndicators: {
    title: "Performance Indicators",
    indicators: {
      resettlement: "Resettlement",
      biodiversity: "Biodiversity",
      protectedAreas: "Protected Areas",
      wetlands: "Wetlands",
      importantSites: "Important Sites",
      monitoringObservations: "Monitoring Observations",
    },
  },
  statistics: {
    title: "Digital Overview",
    titleHighlight: "of Wildlife",
    description:
      "Explore wildlife statistics through the Wildlife Rasid  platform, with accurate data on protected areas and environmental changes to support sustainability and protect biodiversity.",
  },
  aboutManagement: {
    title: "About Management",
    managementTitle: {
      data: "Data Management",
      geo: "Geospatial",
    },
    managementDescription:
      "Providing innovative geospatial solutions and geospatial technical support to ensure the sustainability of wildlife and the preservation of biodiversity in the Kingdom. Working to provide hardware and software for analyzing and managing geospatial information, utilizing the latest technologies and software in geographic information systems, remote sensing, and artificial intelligence",
    tasks: {
      title: {
        tasks: "Tasks",
        management: "Management",
      },
      items: [
        "Develop and implement digital transformation strategies related to geospatial information",
        "Lead digital transformation projects related to geospatial information",
        "Develop and implement solutions and support innovation and development related to geospatial information",
        "Develop and update the Center's geospatial databases",
        "Support spatial analysis to support wildlife conservation projects",
        "Support the monitoring of environmental changes and their impacts on wildlife using modern geospatial technologies",
        "Produce and update environmental and topographic maps of areas of interest in coordination with relevant departments",
        "Collaborate with other departments and divisions to leverage and effectively utilize geospatial data",
        "Provide training and technical support to users of remote sensing and geographic information systems (GIS) technologies at the Center",
        "Participate in developing strategic plans related to geospatial technology to preserve biodiversity in the Kingdom",
        "Develop and update software for geospatial applications",
      ],
    },
  },
  services: {
    breadcrumb: "Wildlife Rasid  Platform Services",
    title: "Wildlife Rasid  Platform",
    titleHighlight: "Services",
    description:
      "The Wildlife Rasid  platform provides an integrated set of digital services designed to support wildlife protection",
    serviceItems: [
      {
        name: "Wildlife Rasid   Explorer",
        text: "A service that provides a number of geospatial tools and functions via interactive maps",
      },
      {
        name: "Maps and Data Request",
        text: "An application that allows users to submit requests for specific geospatial data and map printing",
      },
      {
        name: "Wildlife Monitor",
        text: "An application that runs on smart devices to Rasid  and collect biodiversity data and important environmental sites, and to record the field routes of researchers and interested parties",
      },
      {
        name: "Decision Support",
        text: "A service for displaying environmental data and indicators in interactive dashboards",
      },
      {
        name: "Data Analysis",
        text: "Coming soon...",
      },
      {
        name: "Integration",
        text: "Coming soon...",
      },
      {
        name: "Open Data",
        text: "Coming soon...",
      },
      {
        name: "Data Update",
        text: "Coming soon...",
      },
      {
        name: "Documents",
        text: "Coming soon...",
      },
      {
        name: "Field Studies",
        text: "Coming soon...",
      },
      {
        name: "Violations in Protected Areas",
        text: "Coming soon...",
      },
      {
        name: "User Management",
        text: "Coming soon...",
      },
    ],
    comingSoon: "Coming soon...",
  },
  map2: {
    title: "Map of Wildlife",
    titleHighlight: "Interactive",
    description:
      "The interactive map provides accurate and comprehensive data about wildlife",
    stats: {
      seaArea: "of Kingdom's Marine Area",
      landArea: "of Kingdom's Land Area",
    },
    tabs: {
      protectedAreas: "Protected Areas",
      environmentalRegions: "Environmental Regions",
    },
    environmentalRegions: {
      subtitle: "Discover Environmental Diversity",
      title: "Environmental Regions",
      description:
        "The Kingdom is characterized by unique environmental diversity that includes marine and terrestrial areas, where ecosystems vary from sandy beaches to towering mountains and vast deserts.",
      statistics: {
        plants: "Number of Plants",
        animals: "Number of Animals",
      },
      regionDetails: "Region Details",
      additionalInfo: {
        area: "Area",
        elevation: "Elevation",
        depth: "Depth",
        temperature: "Temperature",
        coastline: "Coastline",
        wetlands: "Wetlands",
      },
      regions: {
        mountainForests: {
          title: "Mountain Forests",
          subtitle: "Highland Ecosystems",
          description:
            "Rich biodiversity in mountainous regions with diverse flora and fauna adapted to high-altitude conditions.",
        },
        redSea: {
          title: "Red Sea",
          subtitle: "Marine Ecosystems",
          description:
            "Diverse coral reefs and marine life including endangered species and unique underwater habitats.",
        },
        arabianGulf: {
          title: "Arabian Gulf",
          subtitle: "ARABIAN GULF",
          description:
            "Located in the east of the Kingdom along the Arabian Gulf coast, it is characterized by a hot, humid climate and saline soils with widespread sabkhas. It contains the Kingdom's most important oil and gas resources and includes major cities such as Dammam, Khobar, and Jubail.",
        },
        arabianSands: {
          title: "Arabian Sand Deserts",
          subtitle: "ARABIAN SAND DESERTS",
          description:
            "The Arabian sand deserts ecoregion is comprised of low- to medium-altitude windblown sands in the Kingdom's arid to hyper-arid interior and reaches the Arabian Gulf coast in the Gulf of Salwa. The northern parts are relatively cool with frosts on winter nights and contain some Irano-Turanian and Mediterranean elements among their predominantly Saharo-Arabian species, while the southern parts are hot and hyper-arid with some Afrotropical affinities. Characteristic species include Calligonum comosum, Calligonum crinitum, Cornulaca arabica, Tribulus arabicus, Cyperus spp., Limeum arabicum, Haloxylon persicum, Artemisia monosperma, Scrophularia hypericifolia, Stipagrostis drarii, Centropodia spp., Sand Boa Eryx jayakari, Arabian Sand Viper Cerastes gasperetti, Eastern Sand Skink Scincus mitranus, Arabian Toad-headed Agama Phrynocephalus arabicus, Greater Hoopoe Lark Alaemon alaudipes, Rüppell's Sand Fox Vulpes rueppellii, Sand Cat Felis margarita, Arabian Hare Lepus capensis arabicus, Sand Gazelle Gazella marica, and Arabian Oryx Oryx leucoryx.",
        },
        westernCoastalPlain: {
          title: "Western Coastal Plain",
          subtitle: "WESTERN COASTAL PLAIN",
          description:
            "Extends along the western coast of the Kingdom adjacent to the Red Sea, characterized by its flat terrain and sandy and clay soils. It includes major cities such as Jeddah and Yanbu, and is considered one of the most important urban and economic zones in the Kingdom.",
        },
        arabianGulfCoastalPlain: {
          title: "Arabian Gulf Coastal Plain",
          subtitle: "ARABIAN GULF COASTAL PLAIN",
          description:
            "The Arabian Gulf coastal plain ecoregion occupies the arid low-altitude plains and plateaus east of the plateau of As-Summan and west of the Arabian Gulf shoreline and is characterized by species of Saharo-Arabian origin with some Irano-Turanian and Indomalayan elements. Its southern part is divided into coastal and inland sectors, which are separated by the sand dunes of Al-Jafurah, which pertain to the Arabian sand deserts ecoregion. Characteristic species include Salicornia europaea, Zygophyllum qatarense, Panicum turgidum, glossy-bellied racer Platyceps ventromaculatus, Indian roller Coracias benghalensis, Indian grey mongoose Herpestes edwardsii and golden jackal Canis aureus.",
        },
        westernHighlands: {
          title: "Western Highlands",
          subtitle: "Mountain Ecosystems",
          description:
            "High biodiversity mountainous area with unique geological formations and diverse wildlife habitats.",
        },
        arabianPlateaus: {
          title: "Southwest Arabian Escarpment Shrublands and Woodlands",
          subtitle: "SOUTHWEST ARABIAN ESCARPMENT SHRUBLANDS AND WOODLANDS",
          description:
            "The Southwest Arabian Escarpment shrublands and woodlands ecoregion covers the low- to medium-altitude western slopes of the Sarawat Escarpment south of the Makkah Gap. It contains important bioclimatic refugia that represent the Somalia-Masai Regional Center of Endemism and support patches of Arabian valley forest. Characteristic species include Senegalia asak, Vachellia etbaica, Vachellia johnwoodii, Commiphora spp., Aloe spp., Adenium obesum, Delonix elata, Tarchonanthus camphoratus, Teclea nobilis, Barbeya oleoides, Olea europaea, Mimusops laurifolia, Breonadia salicina, Anderson's rock agama Acanthocercus adramitanus, Yemen monitor lizard Varanus yemenensis, Arabian partridge Alectoris melanocephala, Verreaux's eagle Aquila verreauxii, African grey hornbill Lophoceros nasutus, desert tawny owl Strix hadorami, Bruce's green pigeon Treron waalia, Arabian woodpecker Dendropicos dorae, African paradise flycatcher Terpsiphone viridis, violet-backed starling Cinnyricinclus leucogaster, Arabian grosbeak Rhynchostruthus percivali, Arabian sunbird Cinnyris hellmayri, Arabian waxbill Estrilda rufibarba, Hamadryas baboon Papio hamadryas, rock hyrax Procavia capensis, Blanford's fox Vulpes cana, small-spotted genet Genetta genetta, striped hyena Hyaena hyaena, caracal Caracal caracal, and possibly Arabian leopard Panthera pardus nimr.",
        },
        northernRegionDesert: {
          title: "North Arabian Desert",
          subtitle: "NORTH ARABIAN DESERT",
          description:
            "The North Arabian desert ecoregion covers low- to medium-altitude areas in the Kingdom's arid northern interior and extends deeply into Jordan and Iraq. It is relatively cool, with frequent frosts on winter nights, and its species are of Saharo-Arabian, Irano-Turanian, and some Mediterranean origins. Characteristic species include Artemisia sieberi, Achillea fragrantissima, Salsola tetrandra, Traganum nudatum, Agathophora alopecuroides, Prunus arabica, Anthemis deserti, Deverra triradiata, Ferula spp., Retama raetam, horny-scaled agama Trapelus ruderatus, Persian horned viper Pseudocerastes persicus, see-see partridge Ammoperdix griseogularis, Asian houbara Chlamydotis macqueenii, pin-tailed sandgrouse Pterocles alchata, cream-colored courser Cursorius cursor, Caracal caracal, striped hyena Hyaena hyaena.",
        },
      },
    },
    points: [
      {
        name: "Mountain Forests and Woodland Areas",
        description:
          "Areas rich in plant and animal diversity in mountainous highlands",
        type: "Mountainous",
      },
      {
        name: "Red Sea",
        description: "Marine area rich in coral reefs and diverse marine life",
        type: "Marine",
      },
      {
        name: "Arabian Gulf",
        description:
          "Important marine area for migratory birds and marine life",
        type: "Marine",
      },
      {
        name: "Arabian Sands",
        description:
          "The largest sand desert in the world with unique desert life diversity",
        type: "Desert",
      },
      {
        name: "Western Coastal Plain",
        description: "Coastal area rich in marine life and coastal plants",
        type: "Coastal",
      },
      {
        name: "Arabian Gulf Coastal Plain",
        description:
          "Important coastal area for migratory birds and marine life",
        type: "Coastal",
      },
      {
        name: "Western Highlands",
        description:
          "Mountainous area with high biodiversity and natural landscapes",
        type: "Mountainous",
      },
      {
        name: "Arabian Plateaus",
        description:
          "Plateau area in the center of the Kingdom with diverse wildlife",
        type: "Plateau",
      },
      {
        name: "Northern Region Desert",
        description: "Vast desert area with unique desert life diversity",
        type: "Desert",
      },
    ],
    legend: {
      ncwProtected: "NCW Protected",
      nationalParks: "National Parks",
      alulaProtected: "AlUla Protected",
      royalProtected: "Royal Protected",
      otherProtected: "Other Protected",
      ncw2025: "NCW 2025",
      other2025: "Other 2025",
      ncw2030: "NCW 2030",
    },
  },
  explorerDetailsPage: {
    breadcrumb: "Wildlife Rasid   Explorer Service",
    title: "Wildlife Rasid   Explorer Service",
    description:
      "It is a geographical explorer for wildlife that allows citizens and users to browse and inquire about geographical information provided by the National Center for Wildlife Development. It also provides a set of geographical services that contribute to the completion of daily work for the center's employees and government agencies with higher quality and in less time in an easy and accessible manner that helps officials in managing business and supporting decision-making. The geographical explorer also contains a set of geographical tools that can be adapted to conduct and implement various inquiries and analyses based on the information available in the geographical database, and then make the results of inquiries and analyses available as maps, graphs, diagrams and reports. It also allows the user to print maps according to selected templates and their different measurements.",
    procedures: {
      title: "Service Procedures",
      steps: [
        "The user logs in to the Wildlife Rasid  platform",
        "The user opens the services screen",
        "The user selects the Wildlife Rasid   Explorer application",
        "The user explores the application features as follows:",
      ],
      features: [
        "General search",
        "Quick search",
        "Metadata search",
        "Coordinate search",
        "Map layers",
        "Interactive map",
        "Measurement tools",
        "Map printing",
        "Layer comparison",
        "Map key",
        "Map gallery",
        "Query",
        "Printing",
        "Show Google Maps",
        "Mini map",
        "General site map",
      ],
    },
    beneficiaries: {
      title: "Service Beneficiaries",
      list: [
        "Departments of the National Center for Wildlife Development",
        "Rovers",
        "Researchers",
        "Students and universities",
        "General user",
      ],
    },
    terms: {
      title: "Terms and Conditions",
      description:
        "Compliance with the terms and conditions of the National Center for Wildlife Development and the data sharing agreement.",
    },
    serviceTime: {
      title: "Service Provision Times",
      description:
        "The system receives requests 24 hours a day, but requests will be processed by specialists during the official working hours of the center daily except Friday and Saturday",
    },
    downloadGuide: "To download the user guide",
    startService: "Start Service",
  },
  rassedDetailsPage: {
    breadcrumb: "Wildlife Rasid  Service",
    title: "Wildlife Rasid  Service",
    description:
      'The "Wildlife Monitor" application aims to support monitoring and tracking wildlife and vegetation within natural reserves. This application provides a set of features that make it easier for users to access important information and record data directly and in an organized manner as follows:',
    features: [
      "Identifying reserves",
      "Monitoring biodiversity",
      "Monitoring important sites",
      "Monitoring reports",
      "Recording tracks",
    ],
    procedures: {
      title: "Service Procedures",
      steps: [
        "The user downloads the Wildlife Rasid  application from the electronic store",
        "The user logs in to the application",
        "The user explores the application features as follows:",
      ],
      appFeatures: [
        "The map contains many functions such as (my location - favorites - directions - screen zoom - map guide...)",
        "Search for reserves",
        "Monitor biodiversity",
        "View tracks",
        "View monitoring cases",
        "Account information",
        "Sign out",
      ],
    },
    beneficiaries: {
      title: "Service Beneficiaries",
      list: [
        "Departments of the National Center for Wildlife Development",
        "Researchers",
        "Students and universities",
        "Individuals",
      ],
    },
    terms: {
      title: "Terms and Conditions",
      description:
        "Compliance with the terms and conditions of the National Center for Wildlife Development and the data sharing agreement.",
    },
    serviceTime: {
      title: "Service Provision Times",
      description:
        "The system receives requests 24 hours a day, but requests will be processed by specialists during the official working hours of the center daily except Friday and Saturday",
    },
    downloadGuide: "To download the user guide",
  },
  makeDecisionDetailsPage: {
    breadcrumb: "Decision Making Service",
    title: "Decision Making Service",
    description:
      "Decision Making: An application that provides an interactive dashboard to display data, maps, images, and graphs in real-time, with special tools for modifying, creating, and publishing key performance indicators and templates in a flexible and updatable manner",
    procedures: {
      title: "Service Procedures",
      steps: [
        "The user logs in to the Wildlife Rasid  platform",
        'The user selects the "Decision Making" service',
        "The user explores the dashboard",
        "The user modifies, creates, and publishes key performance indicators and templates",
      ],
    },
    beneficiaries: {
      title: "Service Beneficiaries",
      list: ["Departments of the National Center for Wildlife Development"],
    },
    terms: {
      title: "Terms and Conditions",
      description:
        "Compliance with the terms and conditions of the National Center for Wildlife Development and the data sharing agreement.",
    },
    serviceTime: {
      title: "Service Provision Times",
      description:
        "The system receives requests 24 hours a day, but requests will be processed by specialists during the official working hours of the center daily except Friday and Saturday",
    },
    downloadGuide: "To download the user guide",
    startService: "Start Service",
  },
  requestDataDetailsPage: {
    breadcrumb: "Maps and Data Request Service",
    title: "Maps and Data Request Service",
    description:
      '"Maps and Data Request" is a set of services that allow users to request data and print maps through a number of functions that meet the needs of specialists within the National Center for Wildlife Development, which are:',
    services: [
      "Geospatial data request: Where users within the departments of the National Center for Wildlife Development can request geospatial data from other departments and receive geospatial data requests within the department.",
      "Map printing request: Where users within the departments of the National Center for Wildlife Development can request maps of reserves, terrestrial and marine biodiversity, and others.",
    ],
    procedures: {
      title: "Service Procedures",
      steps: [
        "The user logs in to the Wildlife Rasid  platform",
        "The user opens the services screen",
        "The user selects maps and data request",
        "The user selects the type of request (geospatial data - map printing)",
        "The user submits the request and enters the necessary data",
        "The user reviews the summary",
        "The user sends the request for review",
        "The relevant department reviews the request, takes the necessary actions, and sends the requests",
      ],
    },
    beneficiaries: {
      title: "Service Beneficiaries",
      list: [
        "Departments of the National Center for Wildlife Development",
        "Researchers",
        "Students and universities",
        "Individuals",
      ],
    },
    terms: {
      title: "Terms and Conditions",
      description:
        "Compliance with the terms and conditions of the National Center for Wildlife Development and the data sharing agreement.",
    },
    serviceTime: {
      title: "Service Provision Times",
      description:
        "The system receives requests 24 hours a day, but requests will be processed by specialists during the official working hours of the center daily except Friday and Saturday",
    },
    downloadGuide: "To download the user guide",
    startService: "Start Service",
  },
  loginPage: {
    title: "Login to Natural Observer Platform",
    subtitle: "Access Portal Services",
    nationalAccess: "Login with National Access",
    staffLogin: "National Center for Wildlife Development Staff",
    or: "or",
    usernamePlaceholder: "Username / Email",
    passwordPlaceholder: "Password",
    rememberMe: "Remember me",
    login: "Login",
    forgotPassword: "Forgot Password?",
    createAccount: "Create New Account",
  },
  apps: {
    breadcrumb: "My Applications",
    title: "Apps",
    titleHighlight: "Portal",
    description: "Browse and access your available applications.",
  },
};
