import endpoint from "../api/endpoint";
import { guidesAtom } from "../atoms/guides/guides-atom";
import { IGetAllGuidesResponse } from "../types";

export const getAllGuides = async (search?: string) => {
  try {
    /***
     * start loading
     */
    guidesAtom.startLoading();

    /***
     * fetch data
     */
    const { data } = await endpoint.get<IGetAllGuidesResponse>(
      "/OrganismClassification/GetAll",
      {
        params: {
          q: search,
          // filter_key: i18n.language === "ar" ? "name" : "name_en",
          filter_key: "name",
          contain: 1,
        },
      }
    );

    /***
     * set data, pagination
     */
    guidesAtom.success(data.results, data.totalPages);

    /**
     * end loading
     */
    guidesAtom.endLoading();
  } catch (error) {
    /***
     * set error
     */
    guidesAtom.failed((error as Error).message);

    /**
     * end loading
     */
    guidesAtom.endLoading();
  }
};
