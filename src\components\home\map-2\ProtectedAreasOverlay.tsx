import React, { useState } from "react";
import bg_image_sea from "../../../assets/images/ncw-map/النسبة البحرية.jpg";
import bg_image_land from "../../../assets/images/ncw-map/النسبة البرية.jpg";

interface ProtectedAreasOverlayProps {
  stats: { [key: string]: { sea: string; land: string } };
  selectedInnerTab: string;
  handleInnerTab: (year: string) => void;
  t: (key: string) => string;
}

const years = ["2030", "2025", "2020"];

const legendItems = [
  { color: "#BA8376", key: "ncwProtected" },
  { color: "#C8585D", key: "nationalParks" },
  { color: "#8B8295", key: "alulaProtected" },
  { color: "#187584", key: "royalProtected" },
  { color: "#864317", key: "otherProtected" },
  { color: "#1F6747", key: "ncw2025" },
  { color: "#A97D2B", key: "other2025" },
  { color: "#688D5E", key: "ncw2030" },
];

const ProtectedAreasOverlay: React.FC<ProtectedAreasOverlayProps> = ({
  stats,
  selectedInnerTab,
  handleInnerTab,
  t,
}) => {
  const [hoveredLegend, setHoveredLegend] = useState<number | null>(null);
  return (
    <div
      style={{
        position: "absolute",
        top: "32px",
        // insetInlineStart: "2%",
        insetInlineStart: "0",
        background: "rgba(0, 0, 0, 0.12)",
        padding: "25px",
        borderRadius: "16px",
        color: "#fff",
        zIndex: 10,
        maxWidth: "320px",
        width: "28%",
        maxHeight: "450px",
        textAlign: "start",
        backdropFilter: "blur(10px)",
        boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        transition: "all 0.5s ease-in-out",
        opacity: 1,
        // overflow: "hidden",
        display: "flex",
        flexDirection: "column",
        gap: "18px",
        marginTop: "96px",
        // marginTop: "15px",
      }}
    >
      {/* Stats */}
      <div
        style={{
          display: "flex",
          gap: "12px",
          marginBottom: "10px",
        }}
      >
        <div
          style={{
            // background:
            //   "linear-gradient(135deg, rgba(127, 205, 255, 0.15) 0%, rgba(127, 205, 255, 0.05) 100%)",
            padding: "16px 18px",
            borderRadius: "12px",
            border: "1px solid rgba(127, 205, 255, 0.2)",
            // backdropFilter: "blur(10px)",
            boxShadow: "0 8px 32px rgba(127, 205, 255, 0.1)",
            textAlign: "center",
            minWidth: "90px",
            flex: 1,
            backgroundImage: `url(${bg_image_sea})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        >
          <div
            style={{
              // color: "#7fcdff",
              color: "#fff",
              fontWeight: 800,
              fontSize: "22px",
              marginBottom: "4px",
              textShadow: "0 2px 4px rgba(127, 205, 255, 0.3)",
            }}
          >
            {stats[selectedInnerTab]?.sea}
          </div>
          <div
            style={{
              color: "rgba(255, 255, 255, 0.9)",
              fontWeight: 500,
              fontSize: "12px",
              lineHeight: "1.4",
            }}
          >
            {t("map2.stats.seaArea")}
          </div>
        </div>
        <div
          style={{
            // background:
            //   "linear-gradient(135deg, rgba(180, 84, 52, 0.15) 0%, rgba(180, 84, 52, 0.05) 100%)",
            padding: "16px 18px",
            borderRadius: "12px",
            border: "1px solid rgba(180, 84, 52, 0.2)",
            // backdropFilter: "blur(10px)",
            boxShadow: "0 8px 32px rgba(180, 84, 52, 0.1)",
            textAlign: "center",
            minWidth: "90px",
            flex: 1,
            backgroundImage: `url(${bg_image_land})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        >
          <div
            style={{
              // color: "#b45434",
              color: "#fff",
              fontWeight: 800,
              fontSize: "22px",
              marginBottom: "4px",
              textShadow: "0 2px 4px rgba(180, 84, 52, 0.3)",
            }}
          >
            {stats[selectedInnerTab]?.land}
          </div>
          <div
            style={{
              color: "rgba(255, 255, 255, 0.9)",
              fontWeight: 500,
              fontSize: "12px",
              lineHeight: "1.4",
            }}
          >
            {t("map2.stats.landArea")}
          </div>
        </div>
      </div>
      {/* Inner Tabs */}
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          gap: "10px",
          justifyContent: "center",
          // marginTop: "8px",
        }}
      >
        {years.map((year) => {
          const isActive = selectedInnerTab === year;
          return (
            <button
              key={year}
              style={{
                border: isActive ? "none" : "2px solid rgba(180, 84, 52, 0.6)",
                outline: "none",
                borderRadius: "999px",
                padding: "7px 18px",
                fontSize: "14px",
                fontWeight: 600,
                background: isActive ? "rgba(180, 84, 52, 0.7)" : "transparent",
                color: isActive ? "#fff" : "rgba(180, 84, 52, 0.8)",
                boxShadow: isActive
                  ? "0 2px 8px rgba(180, 84, 52, 0.08)"
                  : "none",
                cursor: "pointer",
                transition: "all 0.18s cubic-bezier(0.4,0,0.2,1)",
                transform: isActive ? "scale(1.05)" : "scale(1)",
              }}
              onMouseEnter={(e) => {
                if (!isActive)
                  e.currentTarget.style.background = "rgba(180,84,52,0.05)";
              }}
              onMouseLeave={(e) => {
                if (!isActive) e.currentTarget.style.background = "transparent";
              }}
              onClick={() => handleInnerTab(year)}
            >
              {year}
            </button>
          );
        })}
      </div>
      {/* Legend Section */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          margin: "0 auto",
          maxWidth: 340,
          width: "100%",
          alignSelf: "center",
          position: "relative",
        }}
      >
        {/* Legend Bar */}
        <div
          style={{
            position: "relative",
            width: "100%",
            height: "14px",
            overflow: "visible",
          }}
        >
          {legendItems.map((item, idx) => (
            <div
              key={item.key}
              style={{
                position: "absolute",
                top: 0,
                left: `${(idx / legendItems.length) * 100}%`,
                width: `${100 / legendItems.length}%`,
                height: "100%",
                cursor: "pointer",
                transition: "opacity 0.18s cubic-bezier(0.4,0,0.2,1)",
                zIndex: idx,
                background: item.color,
              }}
              onMouseOver={() => setHoveredLegend(idx)}
              onMouseOut={() => setHoveredLegend(null)}
              onFocus={() => setHoveredLegend(idx)}
              onBlur={() => setHoveredLegend(null)}
              tabIndex={0}
              aria-label={t(
                `map2.environmentalRegions.regions.${item.key}.title`
              )}
            />
          ))}

          {/* Tooltip positioned relative to the bar */}
          {hoveredLegend !== null && (
            <div
              style={{
                position: "absolute",
                bottom: "calc(100% + 8px)",
                left: `${
                  (hoveredLegend / legendItems.length) * 100 +
                  100 / legendItems.length / 2
                }%`,
                transform: "translateX(-50%)",
                background:
                  "linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.85) 100%)",
                color: "#fff",
                padding: "8px 16px",
                borderRadius: "8px",
                fontSize: "13px",
                fontWeight: 600,
                whiteSpace: "nowrap",
                zIndex: 50,
                pointerEvents: "none",
                boxShadow:
                  "0 8px 32px rgba(0,0,0,0.4), 0 4px 16px rgba(0,0,0,0.2)",
                border: "1px solid rgba(255,255,255,0.15)",
                backdropFilter: "blur(12px)",
                WebkitBackdropFilter: "blur(12px)",
                minWidth: "120px",
                textAlign: "center",
                letterSpacing: "0.5px",
                lineHeight: "1.4",
              }}
            >
              {t(`map2.legend.${legendItems[hoveredLegend].key}`) ||
                legendItems[hoveredLegend].key}
              <div
                style={{
                  position: "absolute",
                  left: "50%",
                  top: "100%",
                  transform: "translateX(-50%)",
                  width: 0,
                  height: 0,
                  borderLeft: "8px solid transparent",
                  borderRight: "8px solid transparent",
                  borderTop: "8px solid rgba(0,0,0,0.95)",
                }}
              />
            </div>
          )}
        </div>

        {/* Legend Labels */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "0 4px",
            marginTop: "8px",
          }}
        >
          {legendItems.map((item) => (
            <div
              key={item.key}
              style={{
                position: "relative",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                flex: 1,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProtectedAreasOverlay;
