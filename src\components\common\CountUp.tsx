import { useEffect, useRef } from "react";
import { motion, useMotionValue, useTransform, animate } from "framer-motion";
import { useInView } from "framer-motion";

interface CountUpProps {
  from: number;
  to: number;
  duration?: number;
  delay?: number;
  className?: string;
  formatValue?: (value: number) => string;
}

const defaultFormatValue = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1).replace(/\.0$/, "") + "M";
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1).replace(/\.0$/, "") + "K";
  }
  return value.toFixed(0);
};

const CountUp: React.FC<CountUpProps> = ({
  from,
  to,
  duration = 2,
  delay = 0,
  className = "",
  formatValue = defaultFormatValue,
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.3 });
  const count = useMotionValue(from);
  const roundedCount = useTransform(count, (latest) => formatValue(latest));

  useEffect(() => {
    if (isInView) {
      const animation = animate(count, to, {
        duration,
        delay,
        ease: "easeOut",
      });
      return animation.stop;
    } else {
      count.set(from); // Reset when out of view
    }
  }, [count, to, duration, delay, isInView, from]);

  return (
    <motion.span ref={ref} className={className}>
      {roundedCount}
    </motion.span>
  );
};

export default CountUp;
