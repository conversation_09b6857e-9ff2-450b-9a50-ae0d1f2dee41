import { Outlet } from "react-router";
import Footer from "../Footer";
import Navbar from "../Navbar";
import { useEffect, useState } from "react";
import MobileMenu from "../home/<USER>";

export default function BaseLayout() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    // Disable the external script's header manipulation
    // This prevents conflicts with our React component
    const originalAddEventListener = window.addEventListener;
    window.addEventListener = function (
      type: string,
      listener: EventListenerOrEventListenerObject,
      options?: boolean | AddEventListenerOptions
    ) {
      if (
        type === "scroll" &&
        listener.toString().includes("header.classList")
      ) {
        // Skip the external script's scroll event listener that manipulates the header
        return;
      }
      return originalAddEventListener.call(this, type, listener, options);
    };

    return () => {
      // Restore original addEventListener when component unmounts
      window.addEventListener = originalAddEventListener;
    };
  }, []);

  return (
    <>
      <Navbar onMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)} />
      <Outlet />
      <Footer />

      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />
    </>
  );
}
