import React from "react";

interface MapHeaderProps {
  t: (key: string) => string;
  className?: string;
}

const MapHeader: React.FC<MapHeaderProps> = ({ t }) => (
  // <div
  //   className={"header-container" + (className ? ` ${className}` : "")}
  //   style={{
  //     maxWidth: "600px",
  //     marginInline: "20px",
  //     position: "relative",
  //     overflow: "hidden",
  //     border: "2px solid rgba(255, 255, 255, 0.2)",
  //     borderRadius: "20px",
  //     padding: "25px 30px",
  //     background: "rgba(0, 0, 0, 0.1)",
  //     backdropFilter: "blur(10px)",
  //     boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
  //   }}
  // >
  //   {/* Animated background elements */}
  //   <div
  //     style={{
  //       position: "absolute",
  //       top: "-50%",
  //       left: "-50%",
  //       width: "200%",
  //       height: "200%",
  //       background:
  //         "radial-gradient(circle, rgba(180, 84, 52, 0.1) 0%, transparent 50%)",
  //       animation: "float 6s ease-in-out infinite",
  //       zIndex: -1,
  //     }}
  //   />
  //   <div
  //     style={{
  //       position: "absolute",
  //       top: "-30%",
  //       right: "-30%",
  //       width: "160%",
  //       height: "160%",
  //       background:
  //         "radial-gradient(circle, rgba(127, 205, 255, 0.1) 0%, transparent 50%)",
  //       animation: "float 8s ease-in-out infinite reverse",
  //       zIndex: -1,
  //     }}
  //   />
  //   {/* Decorative line */}
  //   <div
  //     style={{
  //       width: "60px",
  //       height: "3px",
  //       background: "linear-gradient(90deg, #b45434 0%, #7fcdff 100%)",
  //       margin: "0 auto 20px auto",
  //       borderRadius: "2px",
  //       boxShadow: "0 4px 12px rgba(180, 84, 52, 0.3)",
  //     }}
  //   />
  //   <h2
  //     style={{
  //       fontWeight: 900,
  //       fontSize: "clamp(28px, 4vw, 36px)",
  //       margin: "0 0 15px 0",
  //       background:
  //         "linear-gradient(135deg, #ffffff 0%, #7fcdff 50%, #b45434 100%)",
  //       WebkitBackgroundClip: "text",
  //       WebkitTextFillColor: "transparent",
  //       backgroundClip: "text",
  //       textShadow: "0 4px 8px rgba(0, 0, 0, 0.3)",
  //       letterSpacing: "-0.02em",
  //       lineHeight: "1.2",
  //     }}
  //   >
  //     {t("map2.title")}{" "}
  //     <span
  //       style={{
  //         background: "linear-gradient(135deg, #b45434 0%, #ff6b35 100%)",
  //         WebkitBackgroundClip: "text",
  //         WebkitTextFillColor: "transparent",
  //         backgroundClip: "text",
  //         textShadow: "0 4px 8px rgba(180, 84, 52, 0.4)",
  //       }}
  //     >
  //       {t("map2.titleHighlight")}
  //     </span>
  //   </h2>
  //   {/* Bottom decorative line */}
  //   <div
  //     style={{
  //       width: "40px",
  //       height: "2px",
  //       background: "linear-gradient(90deg, #7fcdff 0%, #b45434 100%)",
  //       margin: "15px auto 0 auto",
  //       borderRadius: "2px",
  //       boxShadow: "0 4px 12px rgba(127, 205, 255, 0.3)",
  //     }}
  //   />
  // </div>

  <>
    <h2
      style={{
        fontWeight: 900,
        fontSize: "clamp(28px, 4vw, 36px)",
        // margin: "0 0 15px 0",
        letterSpacing: "-0.02em",
        lineHeight: "1.2",
        textAlign: "right",
        // paddingInlineStart: "5rem",
      }}
    >
      {t("map2.title")} <span>{t("map2.titleHighlight")}</span>
    </h2>
  </>
);

export default MapHeader;
