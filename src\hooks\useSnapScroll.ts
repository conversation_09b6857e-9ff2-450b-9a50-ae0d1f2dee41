import { useEffect, useCallback, useRef } from "react";

interface UseSnapScrollOptions {
  sectionIds: string[];
  enabled?: boolean;
  threshold?: number;
  onSectionChange?: (sectionId: string, index: number) => void;
}

/**
 * Hook to implement snap scroll functionality
 * Works with CSS scroll-snap properties for smooth section-to-section scrolling
 */
export const useSnapScroll = ({
  sectionIds,
  enabled = true,
  threshold = 0.5,
  onSectionChange,
}: UseSnapScrollOptions) => {
  const isScrollingRef = useRef(false);
  const currentSectionRef = useRef(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Function to scroll to a specific section
  const scrollToSection = useCallback(
    (sectionId: string, behavior: ScrollBehavior = "smooth") => {
      const section = document.getElementById(sectionId);
      if (section) {
        isScrollingRef.current = true;

        section.scrollIntoView({
          behavior,
          block: "start",
          inline: "nearest",
        });

        // Reset scrolling flag after animation
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        timeoutRef.current = setTimeout(() => {
          isScrollingRef.current = false;
        }, 1000);
      }
    },
    []
  );

  // Function to scroll to next section
  const scrollToNext = useCallback(() => {
    const nextIndex = Math.min(
      currentSectionRef.current + 1,
      sectionIds.length - 1
    );
    if (nextIndex !== currentSectionRef.current) {
      scrollToSection(sectionIds[nextIndex]);
    }
  }, [sectionIds, scrollToSection]);

  // Function to scroll to previous section
  const scrollToPrevious = useCallback(() => {
    const prevIndex = Math.max(currentSectionRef.current - 1, 0);
    if (prevIndex !== currentSectionRef.current) {
      scrollToSection(sectionIds[prevIndex]);
    }
  }, [sectionIds, scrollToSection]);

  // Function to get current active section
  const getCurrentSection = useCallback(() => {
    const scrollPosition = window.scrollY + window.innerHeight * threshold;

    for (let i = sectionIds.length - 1; i >= 0; i--) {
      const section = document.getElementById(sectionIds[i]);
      if (section) {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;

        if (
          scrollPosition >= sectionTop &&
          scrollPosition <= sectionTop + sectionHeight
        ) {
          return { sectionId: sectionIds[i], index: i };
        }
      }
    }

    return { sectionId: sectionIds[0], index: 0 };
  }, [sectionIds, threshold]);

  // Add/remove snap scroll class to body
  useEffect(() => {
    if (enabled) {
      document.body.classList.add("snap-scroll-enabled");
    } else {
      document.body.classList.remove("snap-scroll-enabled");
    }

    return () => {
      document.body.classList.remove("snap-scroll-enabled");
    };
  }, [enabled]);

  // Handle wheel events for snap scrolling
  useEffect(() => {
    if (!enabled) return;

    let wheelTimeout: NodeJS.Timeout;
    let isWheeling = false;

    const handleWheel = (e: WheelEvent) => {
      if (isScrollingRef.current || isWheeling) return;

      // Prevent default scrolling
      e.preventDefault();

      isWheeling = true;

      // Clear existing timeout
      if (wheelTimeout) {
        clearTimeout(wheelTimeout);
      }

      // Determine scroll direction
      const deltaY = e.deltaY;

      if (deltaY > 0) {
        // Scrolling down
        scrollToNext();
      } else if (deltaY < 0) {
        // Scrolling up
        scrollToPrevious();
      }

      // Reset wheeling flag
      wheelTimeout = setTimeout(() => {
        isWheeling = false;
      }, 100);
    };

    // Add wheel event listener with passive: false to allow preventDefault
    document.addEventListener("wheel", handleWheel, { passive: false });

    return () => {
      document.removeEventListener("wheel", handleWheel);
      if (wheelTimeout) {
        clearTimeout(wheelTimeout);
      }
    };
  }, [enabled, scrollToNext, scrollToPrevious]);

  // Handle scroll events to track current section
  useEffect(() => {
    if (!enabled) return;

    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const { sectionId, index } = getCurrentSection();

          if (index !== currentSectionRef.current) {
            currentSectionRef.current = index;
            onSectionChange?.(sectionId, index);
          }

          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [enabled, getCurrentSection, onSectionChange]);

  // Handle touch events for mobile snap scrolling
  useEffect(() => {
    if (!enabled) return;

    let touchStartY = 0;
    let touchEndY = 0;
    let isTouching = false;

    const handleTouchStart = (e: TouchEvent) => {
      touchStartY = e.touches[0].clientY;
      isTouching = true;
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isTouching) return;
      touchEndY = e.touches[0].clientY;
    };

    const handleTouchEnd = () => {
      if (!isTouching || isScrollingRef.current) return;

      const touchDiff = touchStartY - touchEndY;
      const minSwipeDistance = 50;

      if (Math.abs(touchDiff) > minSwipeDistance) {
        if (touchDiff > 0) {
          // Swiped up - scroll to next section
          scrollToNext();
        } else {
          // Swiped down - scroll to previous section
          scrollToPrevious();
        }
      }

      isTouching = false;
    };

    document.addEventListener("touchstart", handleTouchStart, {
      passive: true,
    });
    document.addEventListener("touchmove", handleTouchMove, { passive: true });
    document.addEventListener("touchend", handleTouchEnd, { passive: true });

    return () => {
      document.removeEventListener("touchstart", handleTouchStart);
      document.removeEventListener("touchmove", handleTouchMove);
      document.removeEventListener("touchend", handleTouchEnd);
    };
  }, [enabled, scrollToNext, scrollToPrevious]);

  // Handle keyboard navigation
  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (isScrollingRef.current) return;

      switch (e.key) {
        case "ArrowDown":
        case "PageDown":
          e.preventDefault();
          scrollToNext();
          break;
        case "ArrowUp":
        case "PageUp":
          e.preventDefault();
          scrollToPrevious();
          break;
        case "Home":
          e.preventDefault();
          scrollToSection(sectionIds[0]);
          break;
        case "End":
          e.preventDefault();
          scrollToSection(sectionIds[sectionIds.length - 1]);
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [enabled, scrollToNext, scrollToPrevious, scrollToSection, sectionIds]);

  // Cleanup timeouts
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    scrollToSection,
    scrollToNext,
    scrollToPrevious,
    getCurrentSection,
    currentSection: currentSectionRef.current,
  };
};

export default useSnapScroll;
