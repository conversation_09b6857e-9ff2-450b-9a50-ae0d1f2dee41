import { useEffect, useState } from "react";
import { guidesAtom } from "../../atoms/guides/guides-atom";
import { getAllGuides } from "../../services/get-all-guides";
import { getOneGuideList } from "../../services/get-one-guide-list";
import { currentPageAtom } from "../../atoms/pagination/current-page-atom";
import { selectedGuideAtom } from "../../atoms/guides/selected-guide";
import i18n from "../../locales/i18n";

export default function GuidesList() {
  const { data, error, isLoading } = guidesAtom.useValue();
  const [selectedGuideId, setSelectedGuideId] = useState<number | null>(null);
  const currentPage = currentPageAtom.useValue();

  useEffect(() => {
    /***
     * get all guides
     */
    getAllGuides();

    return () => {
      /***
       * reset guides atom
       */
      guidesAtom.reset();
    };
  }, []);

  if (isLoading) {
    return (
      <p
        style={{ textAlign: "center", fontSize: "20px", marginBlock: "100px" }}
      >
        جاري التحميل...
      </p>
    );
  }

  if (error) {
    return (
      <div
        style={{
          textAlign: "center",
          fontSize: "20px",
          marginBlock: "100px",
          color: "red",
        }}
      >
        {error}
      </div>
    );
  }

  // if (data.length === 0) {
  //   return (
  //     <p
  //       style={{ textAlign: "center", fontSize: "20px", marginBlock: "100px" }}
  //     >
  //       لا توجد دلائل
  //     </p>
  //   );
  // }

  return (
    <>
      <div className="guides-list">
        {data.map((guide) => (
          <div
            key={guide.id}
            className={`guide-item ${
              selectedGuideId === guide.id ? "active" : ""
            }`}
            onClick={() => {
              setSelectedGuideId(guide.id);
              getOneGuideList(Number(guide.id), currentPage);
              selectedGuideAtom.change("guide", guide);
            }}
          >
            <div className="guide-item-content">
              <img
                src={`${window.domain}/wildlifefiles/${guide.img_path}`}
                alt=""
              />
            </div>
            <div className="guide-item-name">
              {i18n.language === "ar" ? guide.name : guide.name_en}
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
